/**
 * YELOR NETWORK 主应用程序
 */

class YelorApp {
    constructor() {
        this.isInitialized = false;
    }

    async init() {
        if (this.isInitialized) return;
        
        console.log('🚀 正在初始化 YELOR NETWORK...');
        
        try {
            await this.loadComponents();
            this.bindEvents();
            this.isInitialized = true;
            console.log('✅ YELOR NETWORK 初始化完成');
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
        }
    }

    async loadComponents() {
        // 加载头部组件
        const headerContainer = document.getElementById('header-container');
        if (headerContainer) {
            try {
                await window.YelorUtils.DOM.loadHTML('/components/header.html', headerContainer);
            } catch (error) {
                console.error('头部组件加载失败，使用备用方案');
                headerContainer.innerHTML = this.getHeaderHTML();
            }
        }

        // 加载页脚组件
        const footerContainer = document.getElementById('footer-container');
        if (footerContainer) {
            footerContainer.innerHTML = this.getFooterHTML();
        }
    }

    getHeaderHTML() {
        return `
            <header class="header">
                <div class="nav-container">
                    <div class="logo-section">
                        <div class="logo-wrapper">
                            <div class="logo-icon"></div>
                            <div class="logo-text">
                                <div class="logo-line">YELOR</div>
                                <div class="logo-line">NETWORK</div>
                            </div>
                        </div>
                    </div>
                    
                    <nav class="nav-menu">
                        <div class="nav-items">
                            <a href="#" class="nav-item active">首页</a>
                            <a href="#" class="nav-item">游玩</a>
                            <a href="#" class="nav-item">导航</a>
                            <a href="#" class="nav-item">公告</a>
                            <a href="#" class="nav-item">排行榜</a>
                            <a href="#" class="nav-item">封禁榜</a>
                            <a href="#" class="nav-item">申诉</a>
                        </div>
                    </nav>
                </div>
            </header>
        `;
    }

    getFooterHTML() {
        return `
            <div class="pre-footer">
                <div class="pre-footer-card">
                    <div class="pre-footer-left">
                        <div class="pre-footer-text">
                            <p style="font-family: 'Poppins', 'Noto Sans SC', sans-serif !important; font-size: 48px !important; font-weight: 900 !important; color: #000 !important; margin: 0 !important; line-height: 1.1 !important;">少年不惧岁月长，</p>
                            <p style="font-family: 'Poppins', 'Noto Sans SC', sans-serif !important; font-size: 48px !important; font-weight: 900 !important; color: #000 !important; margin: 0 !important; line-height: 1.1 !important;">彼方尚有荣光在。</p>
                        </div>
                        <a href="#" class="back-to-top" onclick="window.scrollTo({top: 0, behavior: 'smooth'})">
                            <span class="back-to-top-icon">↑</span>
                            <span>回到顶部</span>
                        </a>
                    </div>
                    <div class="pre-footer-right"></div>
                </div>
            </div>
            
            <footer class="site-footer">
                <div class="footer-container">
                    <div class="footer-logo-wrapper">
                        <div class="footer-logo-img" style="width: 60px; height: 60px; background: linear-gradient(135deg, #01d5ff, #0099cc); border-radius: 50%;"></div>
                    </div>
                    <div class="footer-links-grid">
                        <div class="footer-links-left">
                            <div class="footer-column">
                                <h4>友情链接</h4>
                                <ul>
                                    <li><a href="#">卡里Kalee</a></li>
                                    <li><a href="#">Hypixel</a></li>
                                </ul>
                            </div>
                            <div class="footer-column empty-title">
                                <h4>&nbsp;</h4>
                                <ul>
                                    <li><a href="#">PornHub</a></li>
                                    <li><a href="#">lyxycraft</a></li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="footer-links-right">
                            <div class="footer-column">
                                <h4>其他链接</h4>
                                <ul>
                                    <li><a href="#">支持</a></li>
                                    <li><a href="#">工作</a></li>
                                </ul>
                            </div>
                            <div class="footer-column empty-title">
                                <h4>&nbsp;</h4>
                                <ul>
                                    <li><a href="#">使用条款</a></li>
                                    <li><a href="#">支持</a></li>
                                </ul>
                            </div>
                            <div class="footer-column empty-title">
                                <h4>&nbsp;</h4>
                                <ul>
                                    <li><a href="#">谜你世界</a></li>
                                    <li><a href="#">顾子颉</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="footer-bottom">
                        <div class="footer-info-row">
                            <span class="footer-icp">沪ICP备2024077635号-1</span>
                            <span class="footer-copy">© YELOR 2025</span>
                        </div>
                        <span class="footer-disclaimer">NOT AN OFFICIAL MINECRAFT SERVICE. NOT APPROVED BY OR ASSOCIATED WITH MOJANG OR MICROSOFT.</span>
                    </div>
                </div>
            </footer>
        `;
    }

    bindEvents() {
        // 立刻游玩按钮
        const playBtn = document.getElementById('play-btn');
        if (playBtn) {
            playBtn.addEventListener('click', () => {
                if (window.YelorUtils) {
                    window.YelorUtils.Animation.scrollTo('#game-modes-section', -80);
                } else {
                    document.getElementById('game-modes-section')?.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }

        // 返回顶部按钮
        const backToTopBtn = document.getElementById('back-to-top-btn');
        if (backToTopBtn) {
            backToTopBtn.addEventListener('click', () => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });

            // 滚动显示/隐藏返回顶部按钮
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopBtn.style.display = 'block';
                } else {
                    backToTopBtn.style.display = 'none';
                }
            });
        }
    }
}

// 创建全局应用实例
window.YelorApp = new YelorApp();
