/**
 * 工具函数库 - Utility Functions
 * 包含常用的辅助函数和工具方法
 */

// 防止重复定义
if (!window.YelorUtils) {
    window.YelorUtils = {};
}

/**
 * DOM 操作相关工具
 */
window.YelorUtils.DOM = {
    /**
     * 根据选择器获取元素
     * @param {string} selector - CSS选择器
     * @param {Element} context - 上下文元素，默认为document
     * @returns {Element|null}
     */
    $(selector, context = document) {
        return context.querySelector(selector);
    },

    /**
     * 根据选择器获取所有匹配的元素
     * @param {string} selector - CSS选择器
     * @param {Element} context - 上下文元素，默认为document
     * @returns {NodeList}
     */
    $$(selector, context = document) {
        return context.querySelectorAll(selector);
    },

    /**
     * 创建元素
     * @param {string} tag - 标签名
     * @param {Object} attributes - 属性对象
     * @param {string} content - 内容
     * @returns {Element}
     */
    createElement(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        Object.keys(attributes).forEach(key => {
            if (key === 'className') {
                element.className = attributes[key];
            } else if (key === 'innerHTML') {
                element.innerHTML = attributes[key];
            } else {
                element.setAttribute(key, attributes[key]);
            }
        });
        
        if (content) {
            element.textContent = content;
        }
        
        return element;
    },

    /**
     * 异步加载HTML内容到指定容器
     * @param {string} url - HTML文件URL
     * @param {string|Element} container - 容器选择器或元素
     * @returns {Promise}
     */
    async loadHTML(url, container) {
        try {
            const response = await fetch(url);
            const html = await response.text();
            
            const containerElement = typeof container === 'string' 
                ? this.$(container) 
                : container;
                
            if (containerElement) {
                containerElement.innerHTML = html;
                
                // 执行内联脚本
                const scripts = containerElement.querySelectorAll('script');
                scripts.forEach(script => {
                    const newScript = document.createElement('script');
                    if (script.src) {
                        newScript.src = script.src;
                    } else {
                        newScript.textContent = script.textContent;
                    }
                    document.head.appendChild(newScript);
                    script.remove();
                });
            }
            
            return html;
        } catch (error) {
            console.error('加载HTML失败:', error);
            throw error;
        }
    },

    /**
     * 添加事件监听器
     * @param {Element|string} element - 元素或选择器
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     * @param {Object} options - 选项
     */
    on(element, event, handler, options = {}) {
        const el = typeof element === 'string' ? this.$(element) : element;
        if (el) {
            el.addEventListener(event, handler, options);
        }
    },

    /**
     * 移除事件监听器
     * @param {Element|string} element - 元素或选择器
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    off(element, event, handler) {
        const el = typeof element === 'string' ? this.$(element) : element;
        if (el) {
            el.removeEventListener(event, handler);
        }
    },

    /**
     * 检查元素是否在视口中
     * @param {Element} element - 要检查的元素
     * @returns {boolean}
     */
    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
};

/**
 * 字符串处理工具
 */
window.YelorUtils.String = {
    /**
     * 转义HTML字符
     * @param {string} str - 要转义的字符串
     * @returns {string}
     */
    escapeHTML(str) {
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    },

    /**
     * 截取字符串并添加省略号
     * @param {string} str - 原字符串
     * @param {number} length - 最大长度
     * @param {string} suffix - 后缀，默认为'...'
     * @returns {string}
     */
    truncate(str, length, suffix = '...') {
        if (str.length <= length) {
            return str;
        }
        return str.substring(0, length) + suffix;
    },

    /**
     * 格式化数字
     * @param {number} num - 数字
     * @returns {string}
     */
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },

    /**
     * 生成随机字符串
     * @param {number} length - 长度
     * @returns {string}
     */
    randomString(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
};

/**
 * 时间处理工具
 */
window.YelorUtils.Time = {
    /**
     * 格式化时间
     * @param {Date|string|number} date - 日期
     * @param {string} format - 格式字符串
     * @returns {string}
     */
    format(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    /**
     * 获取相对时间
     * @param {Date|string|number} date - 日期
     * @returns {string}
     */
    relative(date) {
        const now = new Date();
        const target = new Date(date);
        const diff = now - target;
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (seconds < 60) {
            return '刚刚';
        } else if (minutes < 60) {
            return `${minutes}分钟前`;
        } else if (hours < 24) {
            return `${hours}小时前`;
        } else if (days < 7) {
            return `${days}天前`;
        } else {
            return this.format(date, 'YYYY-MM-DD');
        }
    }
};

/**
 * 本地存储工具
 */
window.YelorUtils.Storage = {
    /**
     * 设置本地存储
     * @param {string} key - 键
     * @param {any} value - 值
     * @param {number} expiry - 过期时间（毫秒）
     */
    set(key, value, expiry = null) {
        const item = {
            value: value,
            timestamp: Date.now(),
            expiry: expiry ? Date.now() + expiry : null
        };
        localStorage.setItem(key, JSON.stringify(item));
    },

    /**
     * 获取本地存储
     * @param {string} key - 键
     * @returns {any}
     */
    get(key) {
        try {
            const item = JSON.parse(localStorage.getItem(key));
            if (!item) return null;

            // 检查是否过期
            if (item.expiry && Date.now() > item.expiry) {
                localStorage.removeItem(key);
                return null;
            }

            return item.value;
        } catch (error) {
            return null;
        }
    },

    /**
     * 删除本地存储
     * @param {string} key - 键
     */
    remove(key) {
        localStorage.removeItem(key);
    },

    /**
     * 清空本地存储
     */
    clear() {
        localStorage.clear();
    }
};

/**
 * 动画工具
 */
window.YelorUtils.Animation = {
    /**
     * 平滑滚动到指定元素
     * @param {Element|string} target - 目标元素或选择器
     * @param {number} offset - 偏移量
     * @param {number} duration - 持续时间
     */
    scrollTo(target, offset = 0, duration = 800) {
        const element = typeof target === 'string' 
            ? window.YelorUtils.DOM.$(target) 
            : target;
            
        if (!element) return;

        const start = window.pageYOffset;
        const targetPosition = element.offsetTop + offset;
        const distance = targetPosition - start;
        let startTime = null;

        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const run = ease(timeElapsed, start, distance, duration);
            window.scrollTo(0, run);
            if (timeElapsed < duration) requestAnimationFrame(animation);
        }

        function ease(t, b, c, d) {
            t /= d / 2;
            if (t < 1) return c / 2 * t * t + b;
            t--;
            return -c / 2 * (t * (t - 2) - 1) + b;
        }

        requestAnimationFrame(animation);
    },

    /**
     * 淡入动画
     * @param {Element} element - 元素
     * @param {number} duration - 持续时间
     */
    fadeIn(element, duration = 300) {
        element.style.opacity = '0';
        element.style.display = 'block';
        
        let start = null;
        function animate(timestamp) {
            if (!start) start = timestamp;
            const progress = timestamp - start;
            const opacity = Math.min(progress / duration, 1);
            
            element.style.opacity = opacity;
            
            if (progress < duration) {
                requestAnimationFrame(animate);
            }
        }
        
        requestAnimationFrame(animate);
    },

    /**
     * 淡出动画
     * @param {Element} element - 元素
     * @param {number} duration - 持续时间
     */
    fadeOut(element, duration = 300) {
        let start = null;
        const initialOpacity = parseFloat(getComputedStyle(element).opacity);
        
        function animate(timestamp) {
            if (!start) start = timestamp;
            const progress = timestamp - start;
            const opacity = Math.max(initialOpacity - (progress / duration), 0);
            
            element.style.opacity = opacity;
            
            if (progress < duration) {
                requestAnimationFrame(animate);
            } else {
                element.style.display = 'none';
            }
        }
        
        requestAnimationFrame(animate);
    }
};

/**
 * 通知系统
 */
window.YelorUtils.Notification = {
    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 类型：success, error, warning, info
     * @param {number} duration - 显示时长
     */
    show(message, type = 'info', duration = 3000) {
        const container = window.YelorUtils.DOM.$('#notification-container') || this.createContainer();
        
        const notification = window.YelorUtils.DOM.createElement('div', {
            className: `notification notification-${type}`,
            innerHTML: `
                <div class="notification-content">
                    <span class="notification-message">${message}</span>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
            `
        });
        
        container.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                window.YelorUtils.Animation.fadeOut(notification, 300);
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }
        }, duration);
    },

    /**
     * 创建通知容器
     * @returns {Element}
     */
    createContainer() {
        const container = window.YelorUtils.DOM.createElement('div', {
            id: 'notification-container',
            className: 'notification-container'
        });
        
        document.body.appendChild(container);
        return container;
    },

    /**
     * 成功通知
     * @param {string} message - 消息内容
     */
    success(message) {
        this.show(message, 'success');
    },

    /**
     * 错误通知
     * @param {string} message - 消息内容
     */
    error(message) {
        this.show(message, 'error');
    },

    /**
     * 警告通知
     * @param {string} message - 消息内容
     */
    warning(message) {
        this.show(message, 'warning');
    },

    /**
     * 信息通知
     * @param {string} message - 消息内容
     */
    info(message) {
        this.show(message, 'info');
    }
};

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间
 * @returns {Function}
 */
window.YelorUtils.debounce = function(func, delay) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
};

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间
 * @returns {Function}
 */
window.YelorUtils.throttle = function(func, delay) {
    let lastCall = 0;
    return function (...args) {
        const now = Date.now();
        if (now - lastCall >= delay) {
            lastCall = now;
            return func.apply(this, args);
        }
    };
};

/**
 * 深拷贝函数
 * @param {any} obj - 要拷贝的对象
 * @returns {any}
 */
window.YelorUtils.deepClone = function(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
        return obj.map(item => this.deepClone(item));
    }
    
    if (typeof obj === 'object') {
        const clonedObj = {};
        Object.keys(obj).forEach(key => {
            clonedObj[key] = this.deepClone(obj[key]);
        });
        return clonedObj;
    }
};

// 导出工具函数（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.YelorUtils;
} 