/* 基础样式文件 - Base Styles */

/* 字体导入 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* 自定义字体定义 - MiSans VF */
@font-face {
    font-family: 'MiSans VF';
    src: url('../fonts/MiSans VF.ttf') format('truetype');
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
}

/* 重置樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    background: linear-gradient(135deg, #00d5ff 0%, #0099cc 100%);
    min-height: 100vh;
    color: #333;
}

/* 防止图片被拖拽和选择 */
img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 主要內容區域 */
.main-content {
    margin-top: 65px;
    min-height: calc(100vh - 65px);
    /* 确保所有主要内容板块对齐 */
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 内容板块对齐 */
.game-modes-section,
.news-section,
.bbs-section {
    width: 100%;
    margin: 0;
}

/* 黑边优化 - 减少边框粗细 */
.card-border,
.news-hero-border,
.news-info-border,
.news-list-border {
    border-width: 1px !important;
}

/* 工具类 */
.container {
    width: 100%;
    margin: 0;
    padding: 0 20px;
}

.text-center {
    text-align: center;
}

.hidden {
    display: none;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 过渡动画 */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式断点 */
@media (max-width: 1200px) {
    .container {
        padding: 0 15px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 5px;
    }
}

/* 全局字体应用 - 确保所有文本元素都使用MiSans VF */
h1, h2, h3, h4, h5, h6,
p, span, div, a, button, input, textarea, select,
.nav-item, .hero-title, .hero-subtitle,
.game-mode-title, .game-mode-description,
.news-title, .news-content, .news-date,
.bbs-title, .bbs-subtitle, .bbs-content,
.footer-title, .footer-link, .footer-text,
.play-btn, .button, .btn {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif !important;
}

/* 强制覆盖所有可能的文本元素 */
* {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif !important;
}

/* 特别针对常见的文本类 */
[class*="text"], [class*="title"], [class*="content"],
[class*="description"], [class*="date"], [class*="author"],
[class*="tag"], [class*="label"], [class*="name"] {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif !important;
}