/* BBS论坛区域样式 - BBS Section Styles */

.bbs-section {
    width: 100%;
    margin: 0;
    padding: 0; /* 移除所有内边距，解决板块间隙问题 */
    background-color: #01c8ff;
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
}

.bbs-container {
    width: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 40px; /* 在容器内部添加内边距 */
    min-height: 160px;
    gap: 50px;
}

/* 左侧图标墙 */
.bbs-icon-wall {
    position: relative;
    width: 200px;
    height: 150px;
    flex-shrink: 0;
    background-position: center;
    background-repeat: no-repeat;
}

.bbs-icon {
    position: absolute;
    object-fit: contain;
}

/* 图标位置 */
.icon-1 { width: 30px; height: 30px; left: 45px; top: 10px; }
.icon-2 { width: 30px; height: 30px; left: 140px; top: 5px; }
.icon-3 { width: 30px; height: 30px; left: 10px; top: 55px; }
.icon-4 { width: 30px; height: 30px; left: 125px; top: 105px; }
.icon-5 { width: 30px; height: 30px; left: 35px; top: 115px; }

.bbs-icon-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 70px;
    height: 70px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

/* 右侧主要内容 */
.bbs-main-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px; /* 调整标题、副标题和按钮的间距 */
}

.bbs-title-line {
    display: flex;
    align-items: center;
    gap: 15px; /* 标题和图标的间距 */
}

.bbs-title {
    font-family: 'MiSans VF', 'Poppins', 'Noto Sans SC', sans-serif;
    font-size: 36px;
    font-weight: 600;
    letter-spacing: -2px;
    margin: 0;
    color: #ffffff;
    line-height: 1; /* 确保对齐 */
}

.bbs-subtitle {
    font-family: 'MiSans VF', 'Poppins', 'Noto Sans SC', sans-serif;
    font-size: 36px;
    font-weight: 600;
    letter-spacing: -2px;
    line-height: 1.1;
    margin: 0;
    color: #ffffff;
}

.bbs-title-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    transform: scale(1.05);
}

.bbs-enter-button {
    background-color: #ffffff;
    color: #000000;
    font-family: 'MiSans VF', 'Poppins', 'Noto Sans SC', sans-serif;
    font-weight: 500;
    font-size: 20px;
    padding: 10px 30px;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
}

.bbs-enter-button:hover {
    background-color: #f0f0f0;
    transform: scale(1.05);
}

/* 图标动画效果 */
@keyframes float1 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes float2 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-8px) rotate(-3deg); }
}

@keyframes float3 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-12px) rotate(7deg); }
}

@keyframes float4 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-5deg); }
}

@keyframes float5 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-9px) rotate(4deg); }
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.05); }
}

.icon-1 { animation: float1 3s ease-in-out infinite; }
.icon-2 { animation: float2 3.5s ease-in-out infinite; }
.icon-3 { animation: float3 2.8s ease-in-out infinite; }
.icon-4 { animation: float4 3.2s ease-in-out infinite; }
.icon-5 { animation: float5 2.5s ease-in-out infinite; }
.bbs-icon-center { animation: pulse 2s ease-in-out infinite; }

/* 响应式设计 */
@media (max-width: 768px) {
    .bbs-container {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 20px;
    }
    
    .bbs-icon-wall {
        width: 150px;
        height: 100px;
    }
    
    .icon-1 { width: 20px; height: 20px; left: 30px; top: 5px; }
    .icon-2 { width: 20px; height: 20px; left: 100px; top: 0px; }
    .icon-3 { width: 20px; height: 20px; left: 5px; top: 35px; }
    .icon-4 { width: 20px; height: 20px; left: 90px; top: 70px; }
    .icon-5 { width: 20px; height: 20px; left: 25px; top: 80px; }
    
    .bbs-icon-center {
        width: 50px;
        height: 50px;
    }
    
    .bbs-title,
    .bbs-subtitle {
        font-size: 28px;
    }
    
    .bbs-enter-button {
        font-size: 18px;
        padding: 8px 24px;
    }
}

@media (max-width: 480px) {
    .bbs-title,
    .bbs-subtitle {
        font-size: 24px;
    }
    
    .bbs-enter-button {
        font-size: 16px;
        padding: 6px 20px;
    }
    
    .bbs-icon-wall {
        width: 120px;
        height: 80px;
    }
    
    .icon-1 { width: 16px; height: 16px; left: 24px; top: 4px; }
    .icon-2 { width: 16px; height: 16px; left: 80px; top: 0px; }
    .icon-3 { width: 16px; height: 16px; left: 4px; top: 28px; }
    .icon-4 { width: 16px; height: 16px; left: 72px; top: 56px; }
    .icon-5 { width: 16px; height: 16px; left: 20px; top: 64px; }
    
    .bbs-icon-center {
        width: 40px;
        height: 40px;
    }
}

/* 无障碍访问 */
@media (prefers-reduced-motion: reduce) {
    .bbs-icon,
    .bbs-enter-button {
        animation: none;
        transition: none;
    }
    
    @keyframes float1 {
        0%, 100% { transform: none; }
    }
    
    @keyframes float2 {
        0%, 100% { transform: none; }
    }
    
    @keyframes float3 {
        0%, 100% { transform: none; }
    }
    
    @keyframes float4 {
        0%, 100% { transform: none; }
    }
    
    @keyframes float5 {
        0%, 100% { transform: none; }
    }
    
    @keyframes pulse {
        0%, 100% { transform: none; }
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .bbs-icon {
        border: 1px solid #000;
    }
    
    .bbs-title {
        color: #000;
    }
    
    .bbs-subtitle {
        color: #333;
    }
    
    .bbs-enter-button {
        border: 1px solid #000;
    }
} 