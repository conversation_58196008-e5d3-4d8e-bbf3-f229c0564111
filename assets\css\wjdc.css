/* 问卷调查 (wjdc) 专用样式文件 */

/* 问卷界面图片区域 */
.lottery-page .hero-section .lottery-image-section {
    position: absolute;
    top: 25%;
    left: 70%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 20;
    position: relative;
}

/* 问卷背景图片样式 */
.lottery-page .hero-section .lottery-bg-image {
    max-width: 800px;
    max-height: 600px;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* 问卷内容覆盖层 */
.lottery-page .hero-section .questionnaire-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 25;
    pointer-events: none; /* 允许点击穿透到图片 */
}

/* 为第2、3、4区域启用鼠标事件 */
.lottery-page .hero-section .questionnaire-overlay .section.section-2,
.lottery-page .hero-section .questionnaire-overlay .section.section-3,
.lottery-page .hero-section .questionnaire-overlay .section.section-4 {
    pointer-events: auto; /* 启用区域的鼠标事件 */
}

/* 每个区域占据20%的高度 */
.lottery-page .hero-section .questionnaire-overlay .section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-left: 40px;
    padding-right: 200px; /* 为右侧选项按钮预留空间 */
    box-sizing: border-box;
}

/* 重写第2、3、4区域对齐 - 精确垂直对齐 */
.lottery-page .hero-section .questionnaire-overlay .section-2,
.lottery-page .hero-section .questionnaire-overlay .section-3,
.lottery-page .hero-section .questionnaire-overlay .section-4 {
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch !important; /* 改为stretch，让子元素高度一致 */
    justify-content: flex-start !important;
    padding-left: 60px !important; /* 统一的左边距，确保#号对齐 */
    padding-right: 200px !important;
    box-sizing: border-box !important;
    gap: 0 !important; /* 重置gap，使用固定定位 */
    min-height: 80px !important; /* 设置最小高度确保对齐 */
}

/* 问题内容区域 - 精确高度匹配选择器 */
.lottery-page .hero-section .questionnaire-overlay .section-2 .question-content,
.lottery-page .hero-section .questionnaire-overlay .section-3 .question-content,
.lottery-page .hero-section .questionnaire-overlay .section-4 .question-content {
    width: 200px !important; /* 固定宽度 */
    height: 80px !important; /* 与选择器高度完全一致 */
    flex-shrink: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important; /* 顶部和底部对齐 */
    align-items: flex-start !important;
    box-sizing: border-box !important;
}

/* 选择器容器 - 精确高度匹配问题内容 */
.lottery-page .hero-section .questionnaire-overlay .section-2 .choice-container,
.lottery-page .hero-section .questionnaire-overlay .section-3 .choice-container,
.lottery-page .hero-section .questionnaire-overlay .section-4 .choice-container {
    margin-left: 80px !important; /* 固定间距替代gap */
    width: 150px !important;
    height: 80px !important; /* 与问题内容高度完全一致 */
    flex-shrink: 0 !important;
    pointer-events: auto !important;
    z-index: 100 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important; /* 确保第一个和第三个选项对齐边缘 */
}

/* 问题编号 - 顶部对齐，与选择器第一个选项顶部对齐 */
.lottery-page .hero-section .questionnaire-overlay .section-2 .question-number,
.lottery-page .hero-section .questionnaire-overlay .section-3 .question-number,
.lottery-page .hero-section .questionnaire-overlay .section-4 .question-number {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif !important;
    font-size: 24px !important;
    font-weight: 700 !important;
    color: hsl(192, 100%, 50%) !important;
    margin: 0 !important; /* 移除所有边距 */
    padding: 0 !important;
    text-align: left !important;
    width: 100% !important;
    line-height: 1 !important; /* 紧凑行高 */
    flex-shrink: 0 !important;
}

/* 问题文字容器 - 包含所有问题行 */
.lottery-page .hero-section .questionnaire-overlay .section-2 .question-lines,
.lottery-page .hero-section .questionnaire-overlay .section-3 .question-lines,
.lottery-page .hero-section .questionnaire-overlay .section-4 .question-lines {
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-end !important; /* 底部对齐 */
    flex-grow: 1 !important;
}

/* 问题文字行 - 底部对齐，最后一行与选择器第三个选项底部对齐 */
.lottery-page .hero-section .questionnaire-overlay .section-2 .question-line,
.lottery-page .hero-section .questionnaire-overlay .section-3 .question-line,
.lottery-page .hero-section .questionnaire-overlay .section-4 .question-line {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif !important;
    font-size: 20px !important;
    font-weight: 500 !important;
    color: #000 !important;
    line-height: 1.2 !important;
    margin: 0 !important; /* 移除边距 */
    padding: 0 !important;
    text-align: left !important;
    width: 100% !important;
}

/* 最后一个问题行的特殊处理 */
.lottery-page .hero-section .questionnaire-overlay .section-2 .question-line:last-child,
.lottery-page .hero-section .questionnaire-overlay .section-3 .question-line:last-child,
.lottery-page .hero-section .questionnaire-overlay .section-4 .question-line:last-child {
    margin-bottom: 0 !important; /* 确保底部对齐 */
}

/* 确保选择器内的选项可以接收鼠标事件 */
.lottery-page .hero-section .questionnaire-overlay .section-2 .choice-container .choice-option,
.lottery-page .hero-section .questionnaire-overlay .section-3 .choice-container .choice-option,
.lottery-page .hero-section .questionnaire-overlay .section-4 .choice-container .choice-option {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 101 !important;
}

/* 第2、3、4区域的padding-left已在上面的特殊布局中统一设置为215px */

/* 标题样式 (第1区域) */
.lottery-page .hero-section .questionnaire-overlay .section-1 .title-text {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-size: 28px;
    font-weight: 700;
    color: #000;
    line-height: 1.2;
}

/* 问题编号样式 */
.lottery-page .hero-section .questionnaire-overlay .question-number {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-size: 30px;
    font-weight: 800;
    color: #1D5FFF;
    margin-bottom: 8px;
}

/* 问题文本样式 */
.lottery-page .hero-section .questionnaire-overlay .question-line {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-size: 22px;
    font-weight: 500;
    color: #000;
    line-height: 0.8;
    margin-bottom: 4px;
}

/* 预留区域的占位文本样式 */
.lottery-page .hero-section .questionnaire-overlay .section-3 .question-line,
.lottery-page .hero-section .questionnaire-overlay .section-4 .question-line {
    color: #999;
    font-style: italic;
}

/* ========================================
   问卷区域数字颜色修改
   ======================================== */
.lottery-page .hero-section .questionnaire-overlay .section-2 .question-number,
.lottery-page .hero-section .questionnaire-overlay .section-3 .question-number,
.lottery-page .hero-section .questionnaire-overlay .section-4 .question-number {
    color: #01C8FF !important; /* 修改数字颜色为蓝色 */
}

/* ========================================
   按钮状态管理系统
   ======================================== */
/* 基础提交按钮样式 - 使用多重选择器确保优先级 */
html body .lottery-page .hero-section .questionnaire-overlay .submit-button,
body .lottery-page .hero-section .questionnaire-overlay .submit-button,
.lottery-page .hero-section .questionnaire-overlay .submit-button,
#lotterySubmitButton {
    /* 基础样式 */
    width: 150px !important;
    height: 40px !important;
    border-radius: 40px !important;
    background-color: #000000 !important;
    color: #ffffff !important;
    border: none !important;
    outline: none !important; /* 确保没有焦点边框 */
    cursor: pointer !important;

    /* 位置控制 - 可以修改这些值来移动按钮 */
    position: relative !important;
    top: -4px !important;    /* 垂直位置偏移 */
    left: -130px !important;   /* 水平位置偏移 */
    margin: 0 auto !important; /* 水平居中 */

    /* 文字样式 */
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 16px !important;
    font-weight: 500 !important;

    /* 交互效果 */
    transition: all 0.3s ease;
    user-select: none;
    overflow: hidden !important;

    /* 居中对齐 */
    display: flex;
    align-items: center;
    justify-content: center;

    /* 定位 */
    margin: 0 auto;
}

/* 鼠标悬停效果 - 从鼠标位置向外渲染蓝色 */
html body .lottery-page .hero-section .questionnaire-overlay .submit-button:hover,
body .lottery-page .hero-section .questionnaire-overlay .submit-button:hover,
.lottery-page .hero-section .questionnaire-overlay .submit-button:hover,
#lotterySubmitButton:hover {
    background: #01C8FF !important;
    color: #ffffff !important;
}

/* 渲染动画关键帧 */
@keyframes ripple-expand {
    0% {
        transform: scale(0);
        opacity: 0.8;
    }
    50% {
        opacity: 0.4;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* ========================================
   按钮状态样式 - 点击后显示的状态
   ======================================== */

/* 未登录状态 - 点击后显示 */
.lottery-page .hero-section .questionnaire-overlay .submit-button.lottery-no-login {
    background-color: #999999 !important; /* 灰色 - 未登录 */
    color: #cccccc !important;
    cursor: not-allowed !important;
}

.lottery-page .hero-section .questionnaire-overlay .submit-button.lottery-no-login:hover {
    background-color: #999999 !important;
    transform: none !important;
    box-shadow: none !important;
}

/* 登录且抽奖成功状态 - 点击后显示 */
.lottery-page .hero-section .questionnaire-overlay .submit-button.lottery-true {
    background-color: #00C851 !important; /* 绿色 - 成功 */
    color: #ffffff !important;
    cursor: default !important;
}

.lottery-page .hero-section .questionnaire-overlay .submit-button.lottery-true:hover {
    background-color: #00C851 !important;
    transform: none !important;
    box-shadow: none !important;
}

/* 登录但抽奖失败状态 - 点击后显示 */
.lottery-page .hero-section .questionnaire-overlay .submit-button.lottery-false {
    background-color: #FF4444 !important; /* 红色 - 失败 */
    color: #ffffff !important;
    cursor: default !important;
}

.lottery-page .hero-section .questionnaire-overlay .submit-button.lottery-false:hover {
    background-color: #FF4444 !important;
    transform: none !important;
    box-shadow: none !important;
}

/* 处理中状态 - 点击后短暂显示 */
.lottery-page .hero-section .questionnaire-overlay .submit-button.lottery-processing {
    background-color: #01C8FF !important; /* 蓝色 - 处理中 */
    color: #ffffff !important;
    cursor: wait !important;
}

.lottery-page .hero-section .questionnaire-overlay .submit-button.lottery-processing:hover {
    background-color: #01C8FF !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ========================================
   调试面板 - 左上角
   ======================================== */
.lottery-debug-panel {
    position: fixed;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px;
    border-radius: 8px;
    font-family: 'MiSans VF', monospace;
    font-size: 12px;
    z-index: 9999;
    min-width: 200px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.lottery-debug-panel h4 {
    margin: 0 0 10px 0;
    color: #01C8FF;
    font-size: 14px;
    border-bottom: 1px solid #333;
    padding-bottom: 5px;
    text-align: center;
}

.lottery-debug-panel .debug-status {
    margin-bottom: 15px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    border-left: 3px solid #01C8FF;
}

.lottery-debug-panel .debug-status div {
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
}

.lottery-debug-panel .debug-status div:last-child {
    margin-bottom: 0;
}

.lottery-debug-panel .debug-status .status-label {
    color: #01C8FF;
    font-weight: bold;
}

.lottery-debug-panel .debug-status .status-value {
    color: #ffffff;
}

.lottery-debug-panel .debug-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.lottery-debug-panel .debug-button {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-family: 'MiSans VF', sans-serif;
    transition: all 0.2s ease;
    font-weight: 500;
}

.lottery-debug-panel .debug-button.login-toggle {
    background-color: #01C8FF;
    color: white;
}

.lottery-debug-panel .debug-button.login-toggle:hover {
    background-color: #0099CC;
    transform: translateY(-1px);
}

.lottery-debug-panel .debug-button.success {
    background-color: #00C851;
    color: white;
}

.lottery-debug-panel .debug-button.success:hover {
    background-color: #00A041;
    transform: translateY(-1px);
}

.lottery-debug-panel .debug-button.failure {
    background-color: #FF4444;
    color: white;
}

.lottery-debug-panel .debug-button.failure:hover {
    background-color: #CC3333;
    transform: translateY(-1px);
}

.lottery-debug-panel .debug-button:disabled {
    background-color: #666;
    cursor: not-allowed;
    opacity: 0.6;
}

.lottery-debug-panel .debug-button:disabled:hover {
    transform: none;
}

/* ========================================
   第五区域 - 提交按钮
   ======================================== */
.lottery-page .hero-section .questionnaire-overlay .section-5 {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    pointer-events: auto; /* 启用按钮区域的鼠标事件 */
}

/* 提交按钮样式 */
.lottery-page .hero-section .questionnaire-overlay .section-5 .submit-button {
    /* CSS变量 - 便于后续调整 */
    --button-width: 200px;
    --button-height: 50px;
    --button-border-radius: 21px;
    --button-font-size: 18px;

    /* 基础样式 */
    width: var(--button-width);
    height: var(--button-height);
    border-radius: var(--button-border-radius);
    background-color: #000000;
    color: #ffffff;
    border: none;
    cursor: pointer;

    /* 文字样式 */
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: var(--button-font-size);
    font-weight: 500;

    /* 交互效果 */
    transition: all 0.3s ease;
    user-select: none;

    /* 居中对齐 */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 旧的悬停和点击效果已删除 */

/* 重复的按钮状态样式已删除 - 使用更高优先级的选择器 */

/* ========================================
   问卷区域数字颜色修改
   ======================================== */
.lottery-page .hero-section .questionnaire-overlay .section-2 .question-number,
.lottery-page .hero-section .questionnaire-overlay .section-3 .question-number,
.lottery-page .hero-section .questionnaire-overlay .section-4 .question-number {
    color: #01C8FF !important; /* 修改数字颜色为蓝色 */
}

/* ========================================
   调试面板 - 左上角
   ======================================== */
.lottery-debug-panel {
    position: fixed;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px;
    border-radius: 8px;
    font-family: 'MiSans VF', monospace;
    font-size: 12px;
    z-index: 9999;
    min-width: 200px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.lottery-debug-panel h4 {
    margin: 0 0 10px 0;
    color: #01C8FF;
    font-size: 14px;
    border-bottom: 1px solid #333;
    padding-bottom: 5px;
}

.lottery-debug-panel .debug-status {
    margin-bottom: 15px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.lottery-debug-panel .debug-status div {
    margin-bottom: 5px;
}

.lottery-debug-panel .debug-status .status-label {
    color: #01C8FF;
    font-weight: bold;
}

.lottery-debug-panel .debug-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.lottery-debug-panel .debug-button {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-family: 'MiSans VF', sans-serif;
    transition: all 0.2s ease;
}

.lottery-debug-panel .debug-button.login-toggle {
    background-color: #01C8FF;
    color: white;
}

.lottery-debug-panel .debug-button.login-toggle:hover {
    background-color: #0099CC;
}

.lottery-debug-panel .debug-button.success {
    background-color: #00C851;
    color: white;
}

.lottery-debug-panel .debug-button.success:hover {
    background-color: #00A041;
}

.lottery-debug-panel .debug-button.failure {
    background-color: #FF4444;
    color: white;
}

.lottery-debug-panel .debug-button.failure:hover {
    background-color: #CC3333;
}

.lottery-debug-panel .debug-button:disabled {
    background-color: #666;
    cursor: not-allowed;
    opacity: 0.6;
}

/* 响应式调整图片和文字 */
@media (max-width: 1200px) {
    .lottery-page .hero-section .lottery-bg-image {
        max-width: 700px;
        max-height: 500px;
    }

    .lottery-page .hero-section .questionnaire-overlay .section {
        padding-left: 30px;
        padding-right: 150px;
    }

    /* 中等屏幕下第2、3、4区域对齐调整 */
    .lottery-page .hero-section .questionnaire-overlay .section-2,
    .lottery-page .hero-section .questionnaire-overlay .section-3,
    .lottery-page .hero-section .questionnaire-overlay .section-4 {
        padding-left: 50px !important; /* 中等屏幕减少左边距 */
        min-height: 65px !important; /* 中等屏幕最小高度 */
    }

    .lottery-page .hero-section .questionnaire-overlay .section-2 .question-content,
    .lottery-page .hero-section .questionnaire-overlay .section-3 .question-content,
    .lottery-page .hero-section .questionnaire-overlay .section-4 .question-content {
        width: 160px !important; /* 中等屏幕问题内容宽度 */
        height: 65px !important; /* 与选择器高度一致 */
    }

    .lottery-page .hero-section .questionnaire-overlay .section-2 .choice-container,
    .lottery-page .hero-section .questionnaire-overlay .section-3 .choice-container,
    .lottery-page .hero-section .questionnaire-overlay .section-4 .choice-container {
        margin-left: 120px !important; /* 中等屏幕选择器间距 */
        width: 120px !important;
        height: 65px !important; /* 与问题内容高度一致 */
    }

    .lottery-page .hero-section .questionnaire-overlay .section-1 .title-text {
        font-size: 24px;
    }

    .lottery-page .hero-section .questionnaire-overlay .question-number {
        font-size: 20px;
    }

    .lottery-page .hero-section .questionnaire-overlay .question-line {
        font-size: 18px;
    }

    /* 第五区域按钮中等屏幕适配 */
    .lottery-page .hero-section .questionnaire-overlay .section-5 .submit-button {
        --button-width: 160px;
        --button-height: 40px;
        --button-font-size: 16px;
    }

    /* 调试面板中等屏幕适配 */
    .lottery-debug-panel {
        font-size: 11px;
        padding: 12px;
        min-width: 180px;
    }
}

@media (max-width: 768px) {
    .lottery-page .hero-section .lottery-bg-image {
        max-width: 90vw;
        max-height: 70vh;
        border-radius: 15px;
    }

    .lottery-page .hero-section .questionnaire-overlay .section {
        padding-left: 20px;
        padding-right: 100px;
    }

    /* 移动端第2、3、4区域改为垂直布局 */
    .lottery-page .hero-section .questionnaire-overlay .section-2,
    .lottery-page .hero-section .questionnaire-overlay .section-3,
    .lottery-page .hero-section .questionnaire-overlay .section-4 {
        flex-direction: column !important;
        align-items: flex-start !important;
        padding-left: 30px !important; /* 移动端使用较小的左边距 */
        min-height: auto !important; /* 移动端自适应高度 */
    }

    .lottery-page .hero-section .questionnaire-overlay .section-2 .question-content,
    .lottery-page .hero-section .questionnaire-overlay .section-3 .question-content,
    .lottery-page .hero-section .questionnaire-overlay .section-4 .question-content {
        width: 100% !important; /* 移动端问题内容全宽 */
        height: auto !important; /* 移动端自适应高度 */
        margin-bottom: 10px !important;
    }

    .lottery-page .hero-section .questionnaire-overlay .section-2 .choice-container,
    .lottery-page .hero-section .questionnaire-overlay .section-3 .choice-container,
    .lottery-page .hero-section .questionnaire-overlay .section-4 .choice-container {
        margin-left: 0 !important; /* 移动端选择器无左边距 */
        width: 100px !important;
        height: 50px !important;
    }

    .lottery-page .hero-section .questionnaire-overlay .section-1 .title-text {
        font-size: 20px;
    }

    .lottery-page .hero-section .questionnaire-overlay .question-number {
        font-size: 18px;
        margin-bottom: 6px;
    }

    .lottery-page .hero-section .questionnaire-overlay .question-line {
        font-size: 16px;
        margin-bottom: 3px;
    }

    /* 第五区域按钮移动端适配 */
    .lottery-page .hero-section .questionnaire-overlay .section-5 {
        padding: 15px;
    }

    .lottery-page .hero-section .questionnaire-overlay .section-5 .submit-button {
        --button-width: 140px;
        --button-height: 36px;
        --button-font-size: 14px;
    }

    /* 调试面板移动端适配 */
    .lottery-debug-panel {
        font-size: 10px;
        padding: 10px;
        min-width: 160px;
        top: 5px;
        left: 5px;
    }

    .lottery-debug-panel h4 {
        font-size: 12px;
    }

    .lottery-debug-panel .debug-button {
        padding: 6px 10px;
        font-size: 10px;
    }
}
