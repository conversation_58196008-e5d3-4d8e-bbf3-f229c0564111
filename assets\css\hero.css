/* 英雄區域 - 高端設計 */
.hero-section {
    position: relative;
    width: 100%;
    height: 100vh; /* 进一步压缩到55vh */
    min-height: 550px; /* 最小高度调整为550px */
    overflow: hidden;
    margin-top: 65px;
    margin-bottom: 0;
}

/* 主背景 */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        url('/assets/images/hero-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.hero-content {
    position: relative;
    width: 100%;
    height: 100%;
    max-width: 1920px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}





/* 中央Logo - 调整位置对齐50%分割线 */
.hero-logo {
    position: absolute;
    left: 50%; /* 改为50%对齐中央分割线 */
    transform: translateX(-50%); /* 使用transform居中 */
    top: -5%; /* 稍微下移以对齐分割线 */
    width: 1000px;
    height: 1000px;
    background-image: url('/assets/images/hero-logo.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
}





/* 立刻游玩按鈕 - 调整位置对齐50%分割线 */
.hero-play-button {
    position: absolute;
    left: 50%;
    top: 75%;
    transform: translateX(-50%);
    width: 140px;
    height: 40px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: #000;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 18px;
    overflow: hidden;
    transition: color 0.6s ease;
    z-index: 100; /* 确保按钮在最上层 */
    visibility: visible !important;
    opacity: 1 !important;
}

/* 蓝色渲染动画效果 */
.hero-play-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(1, 213, 255, 0.8) 0%, rgba(1, 213, 255, 0.6) 50%, rgba(1, 213, 255, 0.3) 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
    z-index: -1;
}

.hero-play-button:hover::before {
    width: 200px;
    height: 200px;
}

.hero-play-button:hover {
    color: #fff;
    background: rgba(1, 213, 255, 0.9);
}

.play-btn {
    /* 简化样式，因为父元素已经处理了所有样式 */
    background: transparent;
    border: none;
    color: inherit;
    font: inherit;
    cursor: inherit;
    text-decoration: none;
    display: inline;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 左上角加號 */
.hero-plus {
    position: absolute;
    left: 40px;
    top: 40px;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 300;
    font-size: 35px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 左下角裝飾 */
.hero-decoration {
    position: absolute;
    left: 40px;
    bottom: 40px;
    width: 50px;
    height: 40px;
    opacity: 0.9;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

.decoration-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 右下角箭頭 */
.hero-arrow {
    position: absolute;
    right: 40px;
    bottom: 40px;
    width: 80px;
    height: 20px;
    opacity: 0.9;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

.arrow-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.content-wrapper {
    text-align: center;
    color: #ffffff;
    max-width: 800px;
    padding: 0 20px;
}

.content-wrapper h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.content-wrapper p {
    font-size: 1.2rem;
    opacity: 0.9;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    /* 英雄区域响应式 */
    .hero-section {
        height: 52vh; /* 进一步压缩中等屏幕 */
        min-height: 480px;
    }
    
    .hero-background {
        width: 100%;
        left: 0;
    }
    

    
    .hero-logo {
        left: 50%;
        transform: translateX(-50%);
        top: 25%; /* 调整logo位置 */
    }
    
    .hero-play-button {
        left: 50%;
        transform: translateX(-50%);
        top: 85%; /* 调整按钮位置 */
    }
    

    
    .hero-arrow {
        display: none;
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-top: 120px;
    }
    
    /* 移动端英雄区域 */
    .hero-section {
        height: 55vh; /* 移动端保持合适的比例 */
        min-height: 400px;
    }
    

    
    .hero-logo {
        left: 50%;
        right: auto;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 80px;
        height: 80px;
    }
    
    .hero-play-button {
        left: 50%;
        transform: translateX(-50%);
        top: 85%; /* 调整按钮位置 */
        width: 120px;
    }
    
    .hero-plus {
        left: 20px;
        top: 80px;
        font-size: 30px;
    }
    
    .hero-decoration {
        left: 20px;
        bottom: 20px; /* 改为相对底部定位 */
        width: 40px;
        height: 35px;
    }
    
    .content-wrapper h1 {
        font-size: 2rem;
    }
    
    .content-wrapper p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .content-wrapper h1 {
        font-size: 1.5rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-wrapper {
    animation: fadeInUp 1s ease-out;
} 