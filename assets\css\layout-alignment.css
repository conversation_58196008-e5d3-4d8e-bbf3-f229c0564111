/* 布局对齐优化 - Layout Alignment */

/* 确保所有主要内容板块完美对齐 */
.main-content {
    display: flex;
    flex-direction: column;
    align-items: stretch; /* 让所有子元素占满宽度 */
    width: 100%;
    max-width: 100vw; /* 防止超出视窗宽度 */
    margin: 0;
    overflow-x: hidden; /* 防止水平滚动 */
}

/* 所有主要板块对齐 */
.game-modes-section,
.news-section,
.bbs-section {
    width: 100.1%;
    max-width: 100vw;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
}

/* 游戏模式容器对齐 */
.game-modes-container {
    width: 100%;
    max-width: 100vw;
    margin: 0;
    box-sizing: border-box;
}

/* 新闻区域对齐 */
.news-section {
    width: 100%;
    max-width: 100vw;
    margin: 0;
    box-sizing: border-box;
}

/* BBS区域对齐 */
.bbs-section {
    width: 100%;
}

.bbs-container {
    width: 100%;
    margin: 0 auto;
}

/* 边距统一优化 */
.game-modes-section + .news-section,
.news-section + .bbs-section {
    margin-top: 0;
}

/* 确保所有边框都是1px */
.card-border,
.news-hero-border,
.news-info-border,
.news-list-border,
.news-list-cover,
.socials-section {
    border-width: 1px !important;
}

/* 响应式对齐 */
@media (max-width: 1400px) {
    .main-content {
        width: 100%;
        padding: 0;
    }
    
    .game-modes-container,
    .news-section,
    .bbs-container {
        width: 100%;
        max-width: 100vw;
    }
    
    /* 确保游戏模式和新闻区域始终对齐 */
    .game-modes-section {
        margin-bottom: 0;
    }
    
    .news-section {
        margin-top: 0;
    }
}

@media (max-width: 1200px) {
    .main-content {
        width: 100%;
        padding: 0;
    }
    
    .game-modes-container,
    .news-section,
    .bbs-container {
        width: 100%;
        max-width: 100vw;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 0;
        width: 100%;
        max-width: 100vw;
    }
    
    .game-modes-container,
    .news-section,
    .bbs-container {
        width: 100%;
        max-width: 100vw;
    }
}

/* 垂直边框对齐优化 - 确保游戏区域和新闻区域的边框完美对齐 */
/* 移除重复的边框，避免双重线条 */
.game-cards .game-card:nth-child(3) .card-border {
    border-right: none; /* 第三张游戏卡片不需要右边框，因为社交区域有左边框 */
}