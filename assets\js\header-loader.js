// Header组件加载器
(function() {
    'use strict';
    
    // 加载header组件
    async function loadHeader() {
        try {
            const response = await fetch('/components/header.html');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const headerHTML = await response.text();
            
            // 查找header占位符并替换
            const headerPlaceholder = document.getElementById('header-placeholder');
            if (headerPlaceholder) {
                headerPlaceholder.outerHTML = headerHTML;
            } else {
                // 如果没有占位符，在body开头插入
                document.body.insertAdjacentHTML('afterbegin', headerHTML);
            }
            
            // 设置当前页面的活跃导航项
            setActiveNavItem();
            
        } catch (error) {
            console.error('加载header组件失败:', error);
            // 如果加载失败，显示一个简单的fallback header
            showFallbackHeader();
        }
    }
    
    // 设置活跃的导航项
    function setActiveNavItem() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            item.classList.remove('active');
            const href = item.getAttribute('href');
            
            // 检查当前页面路径
            if (href === currentPath || 
                (currentPath.includes('index') && href.includes('index')) ||
                (currentPath === '/' && href.includes('index'))) {
                item.classList.add('active');
            }
        });
    }
    
    // 备用header（如果组件加载失败）
    function showFallbackHeader() {
        const fallbackHTML = `
            <header class="header">
                <div class="nav-container">
                    <div class="logo-section">
                        <div class="logo-wrapper">
                            <div class="logo-icon"></div>
                            <div class="logo-text">
                                <div class="logo-line">YELOR</div>
                                <div class="logo-line">NETWORK</div>
                            </div>
                        </div>
                    </div>
                    <nav class="nav-menu">
                        <div class="nav-items">
                            <a href="index-new.html" class="nav-item">首页</a>
                            <a href="#" class="nav-item">游戏</a>
                            <a href="#" class="nav-item">商店</a>
                            <a href="#" class="nav-item">论坛</a>
                            <a href="#" class="nav-item">关于</a>
                        </div>
                    </nav>
                </div>
            </header>
        `;
        
        const headerPlaceholder = document.getElementById('header-placeholder');
        if (headerPlaceholder) {
            headerPlaceholder.outerHTML = fallbackHTML;
        } else {
            document.body.insertAdjacentHTML('afterbegin', fallbackHTML);
        }
        
        setActiveNavItem();
    }
    
    // 页面加载完成后加载header
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadHeader);
    } else {
        loadHeader();
    }
})();
