/* 游戏模式样式 - Game Modes Styles */

.game-modes-section {
    width: 100%;
    padding: 0;
    margin: 0;
    background: transparent;
}

.game-modes-container {
    display: flex;
    width: 100%;
    max-width: 100vw;
    height: 30vh; /* 调整为30vh以补偿hero的压缩 */
    min-height: 300px; /* 最小高度300px */
    position: relative;
    margin: 0 auto;
    box-sizing: border-box;
}

/* 游戏卡片区域 */
.game-cards {
    display: flex;
    width: 75%;
    height: 100%;
    gap: 0;
    box-sizing: border-box;
}

.game-card {
    position: relative;
    width: 33.33%;
    height: 100%;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.game-card:hover {
    transform: scale(1.02);
}

/* 卡片背景 */
.card-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.survival-card .card-background {
    background-image: url('/assets/images/news-1.png');
}

.practice-card .card-background {
    background-image: url('/assets/images/news-2.png');
}

.minigames-card .card-background {
    background-image: url('/assets/images/news-3.png');
}

/* 卡片覆盖层 */
.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    mix-blend-mode: screen;
}

.survival-overlay {
    background: rgba(6, 195, 76, 0.63);
}

.practice-overlay {
    background: #b81f00;
}

.minigames-overlay {
    background: rgba(1, 213, 255, 0.71);
}

/* 卡片边框 */
.card-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #000000;
    pointer-events: none;
}

/* 卡片内容 */
.card-content {
    position: absolute;
    left: 22px;
    top: 0;
    width: calc(100% - 44px);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px 0;
    z-index: 10;
}

.player-count {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 800;
    font-size: 18px;
    color: #ffffff;
    letter-spacing: -1.6px;
    line-height: 0.9;
}

.game-title {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 900;
    font-size: 44px;
    color: #000000;
    letter-spacing: -4px;
    line-height: 0.9;
    margin-top: auto;
}

.game-subtitle {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 26px;
    color: #ffffff;
    letter-spacing: -2.3px;
    line-height: 0.9;
    margin-top: 6px;
}

/* 社交媒体区域 - 精确按照Figma设计实现 */
.socials-section {
    position: relative;
    width: 25%;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

/* 蓝色背景 */
.socials-bg {
    position: absolute;
    inset: 0;
    background: #01d5ff;
    border: 1px solid #000000;
}

/* 顶部SOCIALS标签 - 参考NEWS标签样式 */
.socials-tag {
    position: absolute;
    left: 34px;
    top: 2px;
    z-index: 3;
}

.socials-tag-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 125px;
    height: 25px;
    background: #000000;
}

.socials-tag-icon {
    position: absolute;
    left: 7px;
    top: 7px;
    width: 11px;
    height: 11px;
}

.socials-icon-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #ffffff;
}

.socials-icon-line {
    position: absolute;
    background: #ffffff;
    height: 1px;
    width: 7px;
    left: 2px;
}

.socials-icon-line-1 { top: 2px; }
.socials-icon-line-2 { top: 4px; }
.socials-icon-line-3 { top: 6px; width: 4px; }
.socials-icon-line-4 { top: 8px; }

.socials-tag-text {
    position: absolute;
    left: 23px;
    top: 12.5px;
    transform: translateY(-50%);
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 12px;
    color: #ffffff;
    letter-spacing: -1px;
    line-height: 0.88;
    white-space: nowrap;
}

/* 右上角加号 */
.socials-plus-top {
    position: absolute;
    right: 20px;
    top: 20px;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 200;
    font-size: 40px;
    color: #000000;
    line-height: 0.88;
}

/* 左下角加号 */
.socials-plus-bottom {
    position: absolute;
    left: 20px;
    bottom: 20px;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 200;
    font-size: 40px;
    color: #000000;
    line-height: 0.88;
}

/* 主标题 */
.socials-title {
    position: absolute;
    left: 34px;
    top: 58px;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 900;
    font-size: 20px;
    color: #000000;
    letter-spacing: 0;
    line-height: 0.92;
    margin: 0;
    z-index: 2;
}

/* 白色SOCIALS大字背景 */
.socials-large-text {
    position: absolute;
    right: 100px;
    top: 60px;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 40px;
    color: #ffffff;
    letter-spacing: -4.25px;
    line-height: 0.92;
    z-index: 1;
    pointer-events: none;
}

/* 社交图标组 - 使用Figma图片 */
.social-icons {
    position: absolute;
    left: 20px;
    bottom: 90px;
    width: 129px;
    height: 32.59px;
}

.social-icons img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}



/* 装饰线条 - 使用Figma图片 */
.socials-decoration {
    position: absolute;
    right: 20px;
    top: 110px;
    width: 25.469px;
    height: 25.556px;
}

.socials-decoration img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 右下角装饰 - variety.png */
.socials-variety {
    position: absolute;
    right: 0px;
    bottom: -4px;
    width: 80px;
    height: 52px;
}

.socials-variety img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}



/* 响应式设计 */
@media (max-width: 1200px) {
    .game-modes-container {
        width: 100%;
        height: 28vh; /* 调整中等屏幕比例 */
        min-height: 280px; /* 最小高度280px */
    }
    
    .game-title {
        font-size: 36px;
    }
    
    .game-subtitle {
        font-size: 22px;
    }
    
    .player-count {
        font-size: 14px;
    }
    
    .socials-section {
        padding: 12px;
    }
    
    .socials-tag {
        font-size: 11px;
        padding: 5px 10px;
    }
    
    .socials-plus-top {
        font-size: 20px;
        right: 12px;
        top: 12px;
    }
}

@media (max-width: 768px) {
    .game-modes-container {
        width: 100%;
        height: 25vh; /* 移动端保持合适比例 */
        min-height: 200px;
    }
    
    .game-cards {
        width: 70%;
        gap: 0;
    }
    
    .socials-section {
        width: 30%;
        padding: 10px;
    }
    
    .game-title {
        font-size: 36px;
    }
    
    .game-subtitle {
        font-size: 22px;
    }
    
    .player-count {
        font-size: 14px;
    }
    
    .socials-tag {
        font-size: 10px;
        padding: 4px 8px;
        margin-bottom: 8px;
    }
    
    .socials-tag-icon {
        width: 6px;
        height: 6px;
        margin-right: 4px;
    }
    
    .socials-main-content {
        margin: 15px 0;
    }
    
    .social-icon {
        width: 20px;
        height: 20px;
        gap: 6px;
    }
    
    .social-icon::before {
        width: 12px;
        height: 12px;
    }
    
    .socials-plus-top,
    .socials-plus-bottom {
        font-size: 18px;
    }
    
    .socials-plus-top {
        right: 10px;
        top: 10px;
    }
    
    .community-text {
        font-size: 9px;
    }
    
    .community-count {
        font-size: 14px;
    }
    
    .socials-badge-main {
        padding: 4px 8px;
    }
}

@media (max-width: 480px) {
    .game-modes-container {
        width: 100%;
        height: auto;
        flex-direction: column;
    }
    
    .game-cards {
        width: 100%;
        height: 160px;
        gap: 0;
    }
    
    .socials-section {
        width: 100%;
        height: 100px;
        padding: 8px;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
    
    .game-title {
        font-size: 28px;
    }
    
    .game-subtitle {
        font-size: 18px;
    }
    
    .player-count {
        font-size: 12px;
    }
    
    .socials-tag {
        font-size: 9px;
        padding: 3px 6px;
        margin-bottom: 0;
    }
    
    .socials-main-content {
        flex: 1;
        margin: 0 10px;
        align-items: center;
    }
    
    .socials-title {
        text-align: center;
    }
    
    .social-icons {
        justify-content: center;
        margin: 8px 0;
    }
    
    .social-icon {
        width: 18px;
        height: 18px;
    }
    
    .social-icon::before {
        width: 10px;
        height: 10px;
    }
    
    .socials-footer {
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }
    
    .socials-plus-top {
        position: static;
        order: 1;
    }
    
    .socials-plus-bottom {
        order: 3;
    }
    
    .socials-badge-group {
        order: 2;
    }
    
    .community-text {
        font-size: 8px;
    }
    
    .community-count {
        font-size: 12px;
    }
    
    .socials-decoration {
        display: none;
    }
}