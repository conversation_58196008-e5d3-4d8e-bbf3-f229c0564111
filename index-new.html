<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YELOR NETWORK</title>
    
    <!-- 分离的CSS文件 -->
    <link rel="stylesheet" href="/assets/css/base.css">
    <link rel="stylesheet" href="/assets/css/header.css">
    <link rel="stylesheet" href="/assets/css/hero.css">
    <link rel="stylesheet" href="/assets/css/game-modes.css">
    <link rel="stylesheet" href="/assets/css/news.css">
    <link rel="stylesheet" href="/assets/css/bbs.css">
    <link rel="stylesheet" href="/assets/css/footer.css">
    <link rel="stylesheet" href="/assets/css/layout-alignment.css">
    <link rel="stylesheet" href="/assets/css/layout-compact.css">
    <link rel="stylesheet" href="/assets/css/image-scaling-1080p.css">
    
    <!-- JavaScript文件 -->
    <script src="/assets/js/header-loader.js"></script>
    <script src="/assets/js/news.js"></script>
    
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header组件占位符 -->
    <div id="header-placeholder"></div>
    
    <!-- 主要內容區域 -->
    <main class="main-content">
        <!-- 英雄區域 -->
        <section class="hero-section">
            <!-- 主背景 -->
            <div class="hero-background"></div>
            
            <div class="hero-content">
                <!-- 左上角加號 -->
                <div class="hero-plus">+</div>
                

                
                <!-- 中央Logo（不旋轉） -->
                <div class="hero-logo"></div>
                
                <!-- 立刻游玩按鈕（在logo下方） -->
                <a href="lottery.html" class="hero-play-button">
                    <span class="play-btn">立刻游玩</span>
                </a>
                

                
                <!-- 左下角裝飾圖标 -->
                <div class="hero-decoration">
                    <img src="/assets/images/decoration.svg" alt="裝飾" class="decoration-img">
                </div>
                
                <!-- 右下角箭頭 -->
                <div class="hero-arrow">
                    <img src="/assets/images/arrow.svg" alt="箭頭" class="arrow-img">
                </div>
            </div>
        </section>
        
        <!-- 游戏模式区域 -->
        <section class="game-modes-section">
            <div class="game-modes-container">
                <!-- 左侧游戏卡片区域 -->
                <div class="game-cards">
                    <!-- 领域生存卡片 -->
                    <div class="game-card survival-card">
                        <div class="card-background"></div>
                        <div class="card-overlay survival-overlay"></div>
                        <div class="card-border"></div>
                        <div class="card-content">
                            <div class="player-count">5000人正在游玩：</div>
                            <div class="game-title">领域生存</div>
                            <div class="game-subtitle">LINYUSHENCUN</div>
                        </div>
                    </div>
                    
                    <!-- 竞技场卡片 -->
                    <div class="game-card practice-card">
                        <div class="card-background practice-bg"></div>
                        <div class="card-overlay practice-overlay"></div>
                        <div class="card-border"></div>
                        <div class="card-content">
                            <div class="player-count">5123124人正在游玩：</div>
                            <div class="game-title">竞技场</div>
                            <div class="game-subtitle">PRACTICE</div>
                        </div>
                    </div>
                    
                    <!-- 小游戏卡片 -->
                    <div class="game-card minigames-card">
                        <div class="card-background minigames-bg"></div>
                        <div class="card-overlay minigames-overlay"></div>
                        <div class="card-border"></div>
                        <div class="card-content">
                            <div class="player-count">5123124人正在游玩：</div>
                            <div class="game-title">小游戏</div>
                            <div class="game-subtitle">MINI GAMES</div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧社交媒体区域 -->
                <div class="socials-section">
                    <!-- 蓝色背景 -->
                    <div class="socials-bg"></div>
                    
                    <!-- 顶部SOCIALS标签 -->
                    <div class="socials-tag">
                        <div class="socials-tag-bg"></div>
                        <div class="socials-tag-icon">
                            <div class="socials-icon-border"></div>
                            <div class="socials-icon-line socials-icon-line-1"></div>
                            <div class="socials-icon-line socials-icon-line-2"></div>
                            <div class="socials-icon-line socials-icon-line-3"></div>
                            <div class="socials-icon-line socials-icon-line-4"></div>
                        </div>
                        <div class="socials-tag-text">SOCIALS</div>
                    </div>
                    
                    <!-- 右上角加号 -->
                    <div class="socials-plus-top">+</div>
                    
                    <!-- 左下角加号 -->
                    <div class="socials-plus-bottom">+</div>
                    
                    <!-- 右侧装饰线条 - 使用Figma图片 -->
                    <div class="socials-decoration">
                        <img src="http://localhost:3845/assets/0b075fe5e9a7a05c7f936678dea04bd904383d12.svg" alt="装饰线条" />
                    </div>
                    
                    <!-- 社交图标组 - 使用Figma图片 -->
                    <div class="social-icons">
                        <img src="http://localhost:3845/assets/b6431f607fefb78613bc4ffded1ee583899f23db.svg" alt="社交图标组" />
                    </div>
                    
                    <!-- 白色SOCIALS大字背景 -->
                    <div class="socials-large-text">SOCIALS</div>
                    
                    <!-- 主标题 -->
                    <div class="socials-title">关注我们的<br>社交媒体</div>
                    
                    <!-- 右下角装饰 - variety.png -->
                    <div class="socials-variety">
                        <img src="/assets/images/variety.png" alt="装饰图案" />
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 新闻区域 -->
        <section class="news-section">
            <!-- 左侧容器 -->
            <div class="news-left-container">
                <!-- 上半部分：大背景图片区域 -->
                <div class="news-hero">
                    <div class="news-hero-bg"></div>
                    <div class="news-hero-border"></div>
                    
                    <!-- NEWS标签 -->
                    <div class="news-tag-container">
                        <div class="news-tag-bg"></div>
                        <div class="news-tag-icon">
                            <div class="news-icon-border"></div>
                            <div class="news-icon-line news-icon-line-1"></div>
                            <div class="news-icon-line news-icon-line-2"></div>
                            <div class="news-icon-line news-icon-line-3"></div>
                            <div class="news-icon-line news-icon-line-4"></div>
                        </div>
                        <div class="news-tag-text">NEWS</div>
                    </div>
                </div>
                
                <!-- 下半部分：更新信息卡片 -->
                <div class="news-info">
                <div class="news-info-bg"></div>
                <div class="news-info-border"></div>
                
                <!-- 最新信息标签 -->
                <div class="news-latest-tag">
                    <div class="news-latest-bg"></div>
                    <div class="news-latest-star"></div>
                    <div class="news-latest-text">最新信息</div>
                </div>
                
                <!-- 内容区域 -->
                <div class="news-content">
                    <!-- 左侧大字NEWS -->
                    <div class="news-big-text">NEWS</div>
                    
                    <!-- 主要内容 -->
                    <div class="news-main-content">
                        <div class="news-title">服务器重大更新 v2.5.0</div>
                        
                        <div class="news-description">
                            全新的领域生存模式、竞技场系统重做、小游戏扩展包上线。
                            新增建筑系统、经济系统优化、PVP平衡性调整。
                        </div>
                    </div>
                    
                    <!-- 日期和作者信息 -->
                    <div class="news-meta">
                        <div class="news-date">2025/01/15</div>
                        <div class="news-author">作者 - YELOR团队</div>
                    </div>
                    
                    <!-- 箭头 -->
                    <div class="news-arrow"></div>
                    
                    <!-- 装饰元素 -->
                    <div class="news-decoration"></div>
                </div>
            </div>
            </div>
            
            <!-- 右侧大新闻区域 - 新设计 -->
            <div class="main-news-section">
                <div class="main-news-bg"></div>
                <div class="main-news-border"></div>
                
                <!-- NEWS标签 -->
                <div class="main-news-tag">
                    <div class="main-news-tag-bg"></div>
                    <div class="main-news-tag-icon">
                        <div class="main-news-icon-border"></div>
                        <div class="main-news-icon-line main-news-icon-line-1"></div>
                        <div class="main-news-icon-line main-news-icon-line-2"></div>
                        <div class="main-news-icon-line main-news-icon-line-3"></div>
                        <div class="main-news-icon-line main-news-icon-line-4"></div>
                    </div>
                    <div class="main-news-tag-text">NEWS</div>
                </div>
                
                <!-- 新闻项目1 -->
                <div class="main-news-item main-news-item-1">
                    <div class="main-news-large-char">操</div>
                    <div class="main-news-plus main-news-plus-left">+</div>
                    <div class="main-news-content">
                        <div class="main-news-title">操执节</div>
                        <div class="main-news-tag-container">
                            <div class="main-news-item-tag-bg"></div>
                            <div class="main-news-item-tag-icon">
                                <div class="main-news-item-icon-border main-news-item-icon-border-1"></div>
                                <div class="main-news-item-icon-border main-news-item-icon-border-2"></div>
                            </div>
                            <div class="main-news-item-tag-text">游戏更新详情</div>
                        </div>
                        <div class="main-news-description">固执节是只有上海人配参与的节日，当在固执节，所有人都会变得很固执执节是只有上海人配参与的节日，当在固...</div>
                    </div>
                    <div class="main-news-image-container">
                        <div class="main-news-image" style="background-image: url('http://localhost:3845/assets/79389d5d9b341bc7ed4cec8e4175ccbc4f137120.png');"></div>
                        <div class="main-news-image-overlay"></div>
                        <div class="main-news-plus main-news-plus-image">+</div>
                        <div class="main-news-meta">
                            <div class="main-news-date">2025/01/32</div>
                            <div class="main-news-author">作者 - 原力司令</div>
                        </div>
                        <div class="main-news-arrow">
                            <img src="http://localhost:3845/assets/44bb2edafb67bcd87a56e3d18e1ceee25de23860.svg" alt="箭头" />
                        </div>
                    </div>
                    <div class="main-news-decoration">
                        <img src="http://localhost:3845/assets/0b075fe5e9a7a05c7f936678dea04bd904383d12.svg" alt="装饰" />
                    </div>
                </div>
                
                <!-- 新闻项目2 -->
                <div class="main-news-item main-news-item-2">
                    <div class="main-news-large-char">GU</div>
                    <div class="main-news-plus main-news-plus-left">+</div>
                    <div class="main-news-content">
                        <div class="main-news-title">GU执节</div>
                        <div class="main-news-tag-container">
                            <div class="main-news-item-tag-bg"></div>
                            <div class="main-news-item-tag-icon">
                                <div class="main-news-item-icon-border main-news-item-icon-border-1"></div>
                                <div class="main-news-item-icon-border main-news-item-icon-border-2"></div>
                            </div>
                            <div class="main-news-item-tag-text">游戏更新详情</div>
                        </div>
                        <div class="main-news-description">固执节是只有上海人配参与的节日，当在固执节，所有人都会变得很固执执节是只有上海人配参与的节日，当在固...</div>
                    </div>
                    <div class="main-news-image-container">
                        <div class="main-news-image" style="background-image: url('http://localhost:3845/assets/79389d5d9b341bc7ed4cec8e4175ccbc4f137120.png');"></div>
                        <div class="main-news-image-overlay"></div>
                        <div class="main-news-plus main-news-plus-image">+</div>
                        <div class="main-news-meta">
                            <div class="main-news-date">2025/01/32</div>
                            <div class="main-news-author">作者 - 原力司令</div>
                        </div>
                        <div class="main-news-arrow">
                            <img src="http://localhost:3845/assets/44bb2edafb67bcd87a56e3d18e1ceee25de23860.svg" alt="箭头" />
                        </div>
                    </div>
                    <div class="main-news-decoration">
                        <img src="http://localhost:3845/assets/0b075fe5e9a7a05c7f936678dea04bd904383d12.svg" alt="装饰" />
                    </div>
                </div>
                
                <!-- 新闻项目3 -->
                <div class="main-news-item main-news-item-3">
                    <div class="main-news-large-char">顾</div>
                    <div class="main-news-plus main-news-plus-left">+</div>
                    <div class="main-news-content">
                        <div class="main-news-title">顾执节</div>
                        <div class="main-news-tag-container">
                            <div class="main-news-item-tag-bg"></div>
                            <div class="main-news-item-tag-icon">
                                <div class="main-news-item-icon-border main-news-item-icon-border-1"></div>
                                <div class="main-news-item-icon-border main-news-item-icon-border-2"></div>
                            </div>
                            <div class="main-news-item-tag-text">游戏更新详情</div>
                        </div>
                        <div class="main-news-description">固执节是只有上海人配参与的节日，当在固执节，所有人都会变得很固执执节是只有上海人配参与的节日，当在固...</div>
                    </div>
                    <div class="main-news-image-container">
                        <div class="main-news-image" style="background-image: url('http://localhost:3845/assets/79389d5d9b341bc7ed4cec8e4175ccbc4f137120.png');"></div>
                        <div class="main-news-image-overlay"></div>
                        <div class="main-news-plus main-news-plus-image">+</div>
                        <div class="main-news-meta">
                            <div class="main-news-date">2025/01/32</div>
                            <div class="main-news-author">作者 - 原力司令</div>
                        </div>
                        <div class="main-news-arrow">
                            <img src="http://localhost:3845/assets/44bb2edafb67bcd87a56e3d18e1ceee25de23860.svg" alt="箭头" />
                        </div>
                    </div>
                    <div class="main-news-decoration">
                        <img src="http://localhost:3845/assets/0b075fe5e9a7a05c7f936678dea04bd904383d12.svg" alt="装饰" />
                    </div>
                </div>
                
                <!-- 底部"点击了解更多"按钮 -->
                <div class="main-news-more-button">
                    <div class="main-news-more-bg"></div>
                    <div class="main-news-more-dot">
                        <img src="http://localhost:3845/assets/a6826dfcc078913dd81ff2236127c8e0f6996f83.svg" alt="圆点" />
                    </div>
                    <div class="main-news-more-text">点击了解更多</div>
                </div>
            </div>
        </section>
        
        <!-- BBS 论坛区域 (重构) -->
        <section class="bbs-section">
            <div class="bbs-container">
                <!-- 左侧图标墙 -->
                <div class="bbs-icon-wall">
                    <img src="/assets/images/bbs-icon-1.svg" class="bbs-icon icon-1" alt="icon">
                    <img src="/assets/images/bbs-icon-2.svg" class="bbs-icon icon-2" alt="icon">
                    <img src="/assets/images/bbs-icon-3.svg" class="bbs-icon icon-3" alt="icon">
                    <div class="bbs-icon-center" style="background-image: url('/assets/images/logo.png');"></div>
                    <img src="/assets/images/bbs-icon-4.svg" class="bbs-icon icon-4" alt="icon">
                    <img src="/assets/images/bbs-icon-5.svg" class="bbs-icon icon-5" alt="icon">
                </div>
                
                <!-- 右侧主要内容 -->
                <div class="bbs-main-content">
                    <div class="bbs-title-line">
                        <h2 class="bbs-title">YELOR BBS</h2>
                        <img src="/assets/images/bbs-title-icon.svg" class="bbs-title-icon" alt="title icon">
                    </div>
                    <p class="bbs-subtitle">我的世界论坛</p>
                    <a href="#" class="bbs-enter-button">点击进入</a>
                </div>
            </div>
        </section>

    </main>

    <!-- Pre-footer Section -->
    <div class="pre-footer">
        <div class="pre-footer-card">
            <div class="pre-footer-left">
                <div class="pre-footer-text">
                    <p style="font-family: 'Poppins', 'Noto Sans SC', sans-serif !important; font-size: 48px !important; font-weight: 900 !important; color: #000 !important; margin: 0 !important; line-height: 1.1 !important;">少年不惧岁月长，</p>
                    <p style="font-family: 'Poppins', 'Noto Sans SC', sans-serif !important; font-size: 48px !important; font-weight: 900 !important; color: #000 !important; margin: 0 !important; line-height: 1.1 !important;">彼方尚有荣光在。</p>
                </div>
                <a href="#" class="back-to-top">
                    <span class="back-to-top-icon">↑</span>
                    <span>回到顶部</span>
                </a>
            </div>
            <div class="pre-footer-right">
                <!-- 右侧区域，条纹装饰通过 .pre-footer-card::after 实现 -->
            </div>
        </div>
    </div>

    <!-- Main Footer -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-logo-wrapper">
                <img src="/assets/images/logo.png" alt="YELOR Logo" class="footer-logo-img">
            </div>
            <div class="footer-links-grid">
                <!-- 左侧友情链接区域 -->
                <div class="footer-links-left">
                    <div class="footer-column">
                        <h4>友情链接</h4>
                        <ul>
                            <li><a href="#">Kalee Site Club</a></li>
                            <li><a href="#">Minecraft Net</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">Yolo Core Project</a></li>
                            <li><a href="#">Magic Stick Project</a></li>
                        </ul>
                    </div>
                </div>
                
                <!-- 右侧其他链接区域 -->
                <div class="footer-links-right">
                    <div class="footer-column">
                        <h4>其他链接</h4>
                        <ul>
                            <li><a href="#">支持</a></li>
                            <li><a href="#">工作</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">使用条款</a></li>
                            <li><a href="#">开发文档</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">赞助我们</a></li>
                            <li><a href="#">加入我们</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-info-row">
                    <span class="footer-icp">沪ICP备2024077635号-1</span>
                    <span class="footer-copy">© YELOR 2020 ~ 2025</span>
                </div>
                <span class="footer-disclaimer">NOT AN OFFICIAL MINECRAFT SERVICE. NOT APPROVED BY OR ASSOCIATED WITH MOJANG OR MICROSOFT.</span>
            </div>
        </div>
    </footer>
    
    <!-- 缩放系统脚本 -->
    <script>
        function updateScaleInfo() {
            const width = window.innerWidth;
            const scaleFactor = width / 1920;

            // 更新CSS变量
            document.documentElement.style.setProperty('--scale-factor', scaleFactor);
        }

        // 页面加载时和窗口大小改变时更新
        window.addEventListener('load', updateScaleInfo);
        window.addEventListener('resize', updateScaleInfo);

        // 立即执行一次
        updateScaleInfo();
    </script>

    <script src="script.js"></script>
</body>
</html> 