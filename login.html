<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - YELOR NETWORK</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="/assets/css/base.css">
    <link rel="stylesheet" href="/assets/css/header.css">
    <link rel="stylesheet" href="/assets/css/hero.css">
    <link rel="stylesheet" href="/assets/css/game-modes.css">
    <link rel="stylesheet" href="/assets/css/news.css">
    <link rel="stylesheet" href="/assets/css/bbs.css">
    <link rel="stylesheet" href="/assets/css/footer.css">
    <link rel="stylesheet" href="/assets/css/layout-alignment.css">
    <link rel="stylesheet" href="/assets/css/layout-compact.css">
    <link rel="stylesheet" href="/assets/css/image-scaling-1080p.css">
    <link rel="stylesheet" href="/assets/css/lottery.css">
    <link rel="stylesheet" href="/assets/css/login-plant.css">
    <link rel="stylesheet" href="/assets/css/login-layout.css">
    <link rel="stylesheet" href="/assets/css/choicebutton.css">
    
    <!-- JavaScript文件 -->
    <script src="/assets/js/header-loader.js"></script>
    <script src="/assets/js/news.js"></script>
    
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body class="login-page">
    <!-- Header组件占位符 -->
    <div id="header-placeholder"></div>
    
    <!-- 主要內容區域 -->
    <main class="main-content">
        <!-- 英雄區域 - 登录界面 -->
        <section class="hero-section">
            <!-- 主背景 -->
            <div class="hero-background"></div>

            <!-- 登录Logo - 位于login-image-section左边 -->
            <img src="/assets/images/login-logo.png" alt="登录Logo" class="login-logo">

            <!-- 登录界面图片区域 -->
            <div class="login-image-section">
                    <img src="/assets/images/login-bg.png" alt="登录界面" class="lottery-bg-image">

                    <!-- 登录内容覆盖层 -->
                    <div class="questionnaire-overlay">
                        <!-- 第1区域 - 标题 -->
                        <div class="section section-1">
                            <div class="title-text">用户登录</div>
                        </div>

                        <!-- 第2区域 - 用户名 -->
                        <div class="section section-2">
                            <div class="question-content">
                                <div class="question-number">#1</div>
                                <div class="question-lines">
                                    <div class="question-line">用户名</div>
                                </div>
                            </div>
                            <div class="choice-container">
                                <input type="text" class="login-input" placeholder="请输入用户名" id="username">
                            </div>
                        </div>

                        <!-- 第3区域 - 密码 -->
                        <div class="section section-3">
                            <div class="question-content">
                                <div class="question-number">#2</div>
                                <div class="question-lines">
                                    <div class="question-line">密码</div>
                                </div>
                            </div>
                            <div class="choice-container">
                                <input type="password" class="login-input" placeholder="请输入密码" id="password">
                            </div>
                        </div>

                        <!-- 第4区域 - 记住我 -->
                        <div class="section section-4">
                            <div class="question-content">
                                <div class="question-number">#3</div>
                                <div class="question-lines">
                                    <div class="question-line">记住登录状态</div>
                                </div>
                            </div>
                            <div class="choice-container">
                                <label class="checkbox-container">
                                    <input type="checkbox" id="rememberMe">
                                    <span class="checkmark">记住我</span>
                                </label>
                            </div>
                        </div>

                        <!-- 第5区域 - 注册和登录按钮 -->
                        <div class="section section-5">
                            <!-- 注册按钮 -->
                            <button class="register-button" id="registerSubmitButton" onclick="handleRegisterSubmit()">
                                立刻注册
                            </button>
                            <!-- 登录按钮 -->
                            <button class="submit-button" id="loginSubmitButton" onclick="handleLoginSubmit()">
                                立刻登录
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="hero-content">
                <!-- 左上角加號 -->
                <div class="hero-plus">+</div>

                <!-- 左下角裝飾圖标 -->
                <div class="hero-decoration">
                    <img src="/assets/images/decoration.svg" alt="裝飾" class="decoration-img">
                </div>

                <!-- 右下角箭頭 -->
                <div class="hero-arrow">
                    <img src="/assets/images/arrow.svg" alt="箭頭" class="arrow-img">
                </div>
            </div>
        </section>
        
        <!-- BBS 论坛区域 (重构) -->
        <section class="bbs-section">
            <div class="bbs-container">
                <!-- 左侧图标墙 -->
                <div class="bbs-icon-wall">
                    <img src="/assets/images/bbs-icon-1.svg" class="bbs-icon icon-1" alt="icon">
                    <img src="/assets/images/bbs-icon-2.svg" class="bbs-icon icon-2" alt="icon">
                    <img src="/assets/images/bbs-icon-3.svg" class="bbs-icon icon-3" alt="icon">
                    <div class="bbs-icon-center" style="background-image: url('/assets/images/logo.png');"></div>
                    <img src="/assets/images/bbs-icon-4.svg" class="bbs-icon icon-4" alt="icon">
                    <img src="/assets/images/bbs-icon-5.svg" class="bbs-icon icon-5" alt="icon">
                </div>
                
                <!-- 右侧主要内容 -->
                <div class="bbs-main-content">
                    <div class="bbs-title-line">
                        <h2 class="bbs-title">YELOR BBS</h2>
                        <img src="/assets/images/bbs-title-icon.svg" class="bbs-title-icon" alt="title icon">
                    </div>
                    <p class="bbs-subtitle">我的世界论坛</p>
                    <a href="#" class="bbs-enter-button">点击进入</a>
                </div>
            </div>
        </section>

    </main>

    <!-- Pre-footer Section -->
    <div class="pre-footer">
        <div class="pre-footer-card">
            <div class="pre-footer-left">
                <div class="pre-footer-text">
                    <p style="font-family: 'Poppins', 'Noto Sans SC', sans-serif !important; font-size: 48px !important; font-weight: 900 !important; color: #000 !important; margin: 0 !important; line-height: 1.1 !important;">少年不惧岁月长，</p>
                    <p style="font-family: 'Poppins', 'Noto Sans SC', sans-serif !important; font-size: 48px !important; font-weight: 900 !important; color: #000 !important; margin: 0 !important; line-height: 1.1 !important;">彼方尚有荣光在。</p>
                </div>
                <a href="#" class="back-to-top">
                    <span class="back-to-top-icon">↑</span>
                    <span>回到顶部</span>
                </a>
            </div>
            <div class="pre-footer-right">
                <!-- 右侧区域，条纹装饰通过 .pre-footer-card::after 实现 -->
            </div>
        </div>
    </div>

    <!-- Main Footer -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-logo-wrapper">
                <img src="/assets/images/logo.png" alt="YELOR Logo" class="footer-logo-img">
            </div>
            <div class="footer-links-grid">
                <!-- 左侧友情链接区域 -->
                <div class="footer-links-left">
                    <div class="footer-column">
                        <h4>友情链接</h4>
                        <ul>
                            <li><a href="#">Kalee Site Club</a></li>
                            <li><a href="#">Minecraft Net</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">Yolo Core Project</a></li>
                            <li><a href="#">Magic Stick Project</a></li>
                        </ul>
                    </div>
                </div>
                
                <!-- 右侧其他链接区域 -->
                <div class="footer-links-right">
                    <div class="footer-column">
                        <h4>其他链接</h4>
                        <ul>
                            <li><a href="#">支持</a></li>
                            <li><a href="#">工作</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">使用条款</a></li>
                            <li><a href="#">开发文档</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">赞助我们</a></li>
                            <li><a href="#">加入我们</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-info-row">
                    <span class="footer-icp">沪ICP备2024077635号-1</span>
                    <span class="footer-copy">© YELOR 2020 ~ 2025</span>
                </div>
                <span class="footer-disclaimer">NOT AN OFFICIAL MINECRAFT SERVICE. NOT APPROVED BY OR ASSOCIATED WITH MOJANG OR MICROSOFT.</span>
            </div>
        </div>
    </footer>
    
    <!-- 缩放系统脚本 -->
    <script>
        function updateScaleInfo() {
            const width = window.innerWidth;
            const scaleFactor = width / 1920;

            // 更新CSS变量
            document.documentElement.style.setProperty('--scale-factor', scaleFactor);
        }

        // 页面加载时和窗口大小改变时更新
        window.addEventListener('load', updateScaleInfo);
        window.addEventListener('resize', updateScaleInfo);

        // 立即执行一次
        updateScaleInfo();
    </script>

    <!-- 登录功能JavaScript -->
    <script>
        // 登录提交处理函数
        function handleLoginSubmit() {
            console.log('🔑 登录按钮被点击');

            // 获取输入值
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const rememberMe = document.getElementById('rememberMe').checked;

            // 基础验证
            if (!username) {
                alert('请输入用户名');
                return;
            }

            if (!password) {
                alert('请输入密码');
                return;
            }

            // 显示处理中状态
            const button = document.getElementById('loginSubmitButton');
            button.textContent = '登录中...';
            button.disabled = true;

            // 模拟登录API调用
            setTimeout(() => {
                // 这里可以添加实际的登录逻辑
                console.log('登录信息:', {
                    username: username,
                    password: password,
                    rememberMe: rememberMe
                });

                // 模拟登录成功
                button.textContent = '登录成功';
                button.style.backgroundColor = '#00C851';

                // 2秒后跳转或重置
                setTimeout(() => {
                    // 这里可以添加跳转逻辑
                    // window.location.href = '/dashboard.html';

                    // 或者重置按钮状态
                    button.textContent = '立刻登录';
                    button.style.backgroundColor = '#000000';
                    button.disabled = false;

                    alert('登录功能演示完成！');
                }, 2000);

            }, 1500); // 模拟网络延迟
        }

        // 回车键登录
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement && (activeElement.id === 'username' || activeElement.id === 'password')) {
                    handleLoginSubmit();
                }
            }
        });

        // ========================================
        // 注册按钮处理函数
        // ========================================
        function handleRegisterSubmit() {
            console.log('📝 注册按钮被点击');

            // 获取输入值
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            // 基础验证
            if (!username) {
                alert('请输入用户名');
                return;
            }

            if (!password) {
                alert('请输入密码');
                return;
            }

            // 显示处理中状态
            const button = document.getElementById('registerSubmitButton');
            button.textContent = '注册中...';
            button.disabled = true;

            // 模拟注册API调用
            setTimeout(() => {
                console.log('注册信息:', {
                    username: username,
                    password: password
                });

                // 模拟注册成功
                button.textContent = '注册成功';
                button.style.backgroundColor = '#00C851';

                // 2秒后重置
                setTimeout(() => {
                    button.textContent = '立刻注册';
                    button.style.backgroundColor = '#000000';
                    button.disabled = false;

                    alert('注册功能演示完成！');
                }, 2000);

            }, 1500); // 模拟网络延迟
        }

        // ========================================
        // 按钮鼠标悬停效果 - 与lottery按钮相同的从鼠标位置向外渲染蓝色
        // ========================================
        function initLoginButtonHoverEffect() {
            const button = document.getElementById('loginSubmitButton');

            button.addEventListener('mouseenter', function(e) {
                // 改变按钮背景颜色，保持过渡动画
                button.style.background = '#01C8FF';
                button.style.color = '#ffffff';

                // 创建渲染效果元素
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'radial-gradient(circle, rgba(1, 200, 255, 0.6) 0%, rgba(1, 200, 255, 0.3) 50%, transparent 80%)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple-expand 0.5s ease-out forwards';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '1'; // 在按钮文字上方，但透明度较低

                // 获取鼠标相对于按钮的位置
                const rect = button.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 设置渲染效果的起始位置
                const size = Math.max(button.offsetWidth, button.offsetHeight) * 2.5; // 增大尺寸确保完全覆盖
                ripple.style.width = size + 'px';
                ripple.style.height = size + 'px';
                ripple.style.left = (x - size / 2) + 'px';
                ripple.style.top = (y - size / 2) + 'px';

                // 添加到按钮中
                button.appendChild(ripple);

                // 清理函数
                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 600);
            });

            button.addEventListener('mouseleave', function() {
                // 移除内联样式，让CSS hover效果正常工作
                button.style.background = '';
                button.style.color = '';

                // 清理所有渲染效果
                const ripples = button.querySelectorAll('div');
                ripples.forEach(ripple => {
                    // 检查是否是渲染效果元素
                    if (ripple.style.position === 'absolute' && ripple.style.borderRadius === '50%') {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }
                });
            });
        }

        // ========================================
        // 注册按钮鼠标悬停效果 - 与登录按钮完全相同
        // ========================================
        function initRegisterButtonHoverEffect() {
            const button = document.getElementById('registerSubmitButton');

            button.addEventListener('mouseenter', function(e) {
                // 改变按钮背景颜色，保持过渡动画
                button.style.background = '#01C8FF';
                button.style.color = '#ffffff';

                // 创建渲染效果元素
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'radial-gradient(circle, rgba(1, 200, 255, 0.6) 0%, rgba(1, 200, 255, 0.3) 50%, transparent 80%)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple-expand 0.5s ease-out forwards';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '1';

                // 获取鼠标相对于按钮的位置
                const rect = button.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 设置渲染效果的起始位置
                const size = Math.max(button.offsetWidth, button.offsetHeight) * 2.5;
                ripple.style.width = size + 'px';
                ripple.style.height = size + 'px';
                ripple.style.left = (x - size / 2) + 'px';
                ripple.style.top = (y - size / 2) + 'px';

                // 添加到按钮中
                button.appendChild(ripple);

                // 清理函数
                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 600);
            });

            button.addEventListener('mouseleave', function() {
                // 移除内联样式，让CSS hover效果正常工作
                button.style.background = '';
                button.style.color = '';

                // 清理所有渲染效果
                const ripples = button.querySelectorAll('div');
                ripples.forEach(ripple => {
                    if (ripple.style.position === 'absolute' && ripple.style.borderRadius === '50%') {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }
                });
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 登录页面初始化完成');

            // 初始化按钮悬浮特效
            initLoginButtonHoverEffect();
            initRegisterButtonHoverEffect();

            // 聚焦到用户名输入框
            const usernameInput = document.getElementById('username');
            if (usernameInput) {
                usernameInput.focus();
            }
        });
    </script>

</body>
</html>
