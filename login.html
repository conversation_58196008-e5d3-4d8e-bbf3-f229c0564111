<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - YELOR NETWORK</title>
    
    <!-- 分离的CSS文件 -->
    <link rel="stylesheet" href="/assets/css/base.css">
    <link rel="stylesheet" href="/assets/css/header.css">
    <link rel="stylesheet" href="/assets/css/hero.css">
    <link rel="stylesheet" href="/assets/css/game-modes.css">
    <link rel="stylesheet" href="/assets/css/news.css">
    <link rel="stylesheet" href="/assets/css/bbs.css">
    <link rel="stylesheet" href="/assets/css/footer.css">
    <link rel="stylesheet" href="/assets/css/layout-alignment.css">
    <link rel="stylesheet" href="/assets/css/layout-compact.css">
    <link rel="stylesheet" href="/assets/css/image-scaling-1080p.css">
    
    <!-- JavaScript文件 -->
    <script src="/assets/js/header-loader.js"></script>
    <script src="/assets/js/news.js"></script>
    
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header组件占位符 -->
    <div id="header-placeholder"></div>
    
    <!-- 主要內容區域 -->
    <main class="main-content">
        <!-- 英雄區域 - 暂时保留原有结构，为后续登录界面内容预留修改空间 -->
        <section class="hero-section">
            <!-- 主背景 -->
            <div class="hero-background"></div>
            
            <div class="hero-content">
                <!-- 左上角加號 -->
                <div class="hero-plus">+</div>
                
                <!-- 中央Logo（不旋轉） -->
                <div class="hero-logo"></div>
                
                <!-- 登录按钮（替换原来的立刻游玩按钮） -->
                <div class="hero-play-button">
                    <span class="play-btn">用户登录</span>
                </div>
                
                <!-- 左下角裝飾圖标 -->
                <div class="hero-decoration">
                    <img src="/assets/images/decoration.svg" alt="裝飾" class="decoration-img">
                </div>
                
                <!-- 右下角箭頭 -->
                <div class="hero-arrow">
                    <img src="/assets/images/arrow.svg" alt="箭頭" class="arrow-img">
                </div>
            </div>
        </section>
        
        <!-- BBS 论坛区域 (重构) -->
        <section class="bbs-section">
            <div class="bbs-container">
                <!-- 左侧图标墙 -->
                <div class="bbs-icon-wall">
                    <img src="/assets/images/bbs-icon-1.svg" class="bbs-icon icon-1" alt="icon">
                    <img src="/assets/images/bbs-icon-2.svg" class="bbs-icon icon-2" alt="icon">
                    <img src="/assets/images/bbs-icon-3.svg" class="bbs-icon icon-3" alt="icon">
                    <div class="bbs-icon-center" style="background-image: url('/assets/images/logo.png');"></div>
                    <img src="/assets/images/bbs-icon-4.svg" class="bbs-icon icon-4" alt="icon">
                    <img src="/assets/images/bbs-icon-5.svg" class="bbs-icon icon-5" alt="icon">
                </div>
                
                <!-- 右侧主要内容 -->
                <div class="bbs-main-content">
                    <div class="bbs-title-line">
                        <h2 class="bbs-title">YELOR BBS</h2>
                        <img src="/assets/images/bbs-title-icon.svg" class="bbs-title-icon" alt="title icon">
                    </div>
                    <p class="bbs-subtitle">我的世界论坛</p>
                    <a href="#" class="bbs-enter-button">点击进入</a>
                </div>
            </div>
        </section>

    </main>

    <!-- Pre-footer Section -->
    <div class="pre-footer">
        <div class="pre-footer-card">
            <div class="pre-footer-left">
                <div class="pre-footer-text">
                    <p style="font-family: 'Poppins', 'Noto Sans SC', sans-serif !important; font-size: 48px !important; font-weight: 900 !important; color: #000 !important; margin: 0 !important; line-height: 1.1 !important;">少年不惧岁月长，</p>
                    <p style="font-family: 'Poppins', 'Noto Sans SC', sans-serif !important; font-size: 48px !important; font-weight: 900 !important; color: #000 !important; margin: 0 !important; line-height: 1.1 !important;">彼方尚有荣光在。</p>
                </div>
                <a href="#" class="back-to-top">
                    <span class="back-to-top-icon">↑</span>
                    <span>回到顶部</span>
                </a>
            </div>
            <div class="pre-footer-right">
                <!-- 右侧区域，条纹装饰通过 .pre-footer-card::after 实现 -->
            </div>
        </div>
    </div>

    <!-- Main Footer -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-logo-wrapper">
                <img src="/assets/images/logo.png" alt="YELOR Logo" class="footer-logo-img">
            </div>
            <div class="footer-links-grid">
                <!-- 左侧友情链接区域 -->
                <div class="footer-links-left">
                    <div class="footer-column">
                        <h4>友情链接</h4>
                        <ul>
                            <li><a href="#">Kalee Site Club</a></li>
                            <li><a href="#">Minecraft Net</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">Yolo Core Project</a></li>
                            <li><a href="#">Magic Stick Project</a></li>
                        </ul>
                    </div>
                </div>
                
                <!-- 右侧其他链接区域 -->
                <div class="footer-links-right">
                    <div class="footer-column">
                        <h4>其他链接</h4>
                        <ul>
                            <li><a href="#">支持</a></li>
                            <li><a href="#">工作</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">使用条款</a></li>
                            <li><a href="#">开发文档</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">赞助我们</a></li>
                            <li><a href="#">加入我们</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-info-row">
                    <span class="footer-icp">沪ICP备2024077635号-1</span>
                    <span class="footer-copy">© YELOR 2020 ~ 2025</span>
                </div>
                <span class="footer-disclaimer">NOT AN OFFICIAL MINECRAFT SERVICE. NOT APPROVED BY OR ASSOCIATED WITH MOJANG OR MICROSOFT.</span>
            </div>
        </div>
    </footer>
    
    <!-- 缩放系统脚本 -->
    <script>
        function updateScaleInfo() {
            const width = window.innerWidth;
            const scaleFactor = width / 1920;

            // 更新CSS变量
            document.documentElement.style.setProperty('--scale-factor', scaleFactor);
        }

        // 页面加载时和窗口大小改变时更新
        window.addEventListener('load', updateScaleInfo);
        window.addEventListener('resize', updateScaleInfo);

        // 立即执行一次
        updateScaleInfo();
    </script>

</body>
</html>
