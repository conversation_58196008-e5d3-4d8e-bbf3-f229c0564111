<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>抽奖活动 - YELOR NETWORK</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="/assets/css/base.css">
    <link rel="stylesheet" href="/assets/css/header.css">
    <link rel="stylesheet" href="/assets/css/hero.css">
    <link rel="stylesheet" href="/assets/css/game-modes.css">
    <link rel="stylesheet" href="/assets/css/news.css">
    <link rel="stylesheet" href="/assets/css/bbs.css">
    <link rel="stylesheet" href="/assets/css/footer.css">
    <link rel="stylesheet" href="/assets/css/layout-alignment.css">
    <link rel="stylesheet" href="/assets/css/layout-compact.css">
    <link rel="stylesheet" href="/assets/css/image-scaling-1080p.css">
    <link rel="stylesheet" href="/assets/css/lottery.css">
    <link rel="stylesheet" href="/assets/css/wjdc.css">
    <link rel="stylesheet" href="/assets/css/lottery-layout.css">
    <link rel="stylesheet" href="/assets/css/lottery-no-login.css">
    <link rel="stylesheet" href="/assets/css/lottery-receive.css">
    <link rel="stylesheet" href="/assets/css/lottery-noreceive.css">
    <link rel="stylesheet" href="/assets/css/choicebutton.css">

    <!-- JavaScript文件 -->
    <script src="/assets/js/header-loader.js"></script>
    <script src="/assets/js/news.js"></script>
</head>
<body class="lottery-page">
    <!-- Header组件占位符 -->
    <div id="header-placeholder"></div>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 调试面板 - 左上角 -->
        <div class="lottery-debug-panel" id="lotteryDebugPanel">
            <h4>抽奖调试面板</h4>
            <div class="debug-status">
                <div><span class="status-label">登录状态:</span> <span class="status-value" id="debugLoginStatus">未登录</span></div>
                <div><span class="status-label">抽奖结果:</span> <span class="status-value" id="debugLotteryResult">未抽奖</span></div>
                <div><span class="status-label">按钮状态:</span> <span class="status-value" id="debugButtonStatus">默认</span></div>
            </div>
            <div class="debug-controls">
                <button class="debug-button login-toggle" onclick="toggleLoginStatus()">切换登录状态</button>
                <button class="debug-button success" onclick="simulateLotterySuccess()" id="successBtn">模拟抽奖成功</button>
                <button class="debug-button failure" onclick="simulateLotteryFailure()" id="failureBtn">模拟抽奖失败</button>
            </div>
        </div>

        <!-- 抽奖主要内容区域 -->
        <section class="lottery-hero">
            <div class="lottery-container">
                <!-- 左侧内容 -->
                <div class="lottery-content">
                    <div class="lottery-header">
                        <h1 class="lottery-title">YELOR NETWORK</h1>
                        <h2 class="lottery-subtitle">抽奖活动</h2>
                    </div>
                    
                    <div class="lottery-description">
                        <p>参与我们的抽奖活动，有机会获得丰厚奖励！</p>
                        <p>每位用户每天可参与一次抽奖机会。</p>
                    </div>
                    
                    <!-- 选择器组件 -->
                    <div class="lottery-form">
                        <div class="form-group">
                            <label class="form-label">选择游戏模式</label>
                            <div class="choice-container">
                                <div class="choice-option" onclick="selectChoice(this)">
                                    <span class="choice-text">生存模式</span>
                                </div>
                                <div class="choice-option" onclick="selectChoice(this)">
                                    <span class="choice-text">创造模式</span>
                                </div>
                                <div class="choice-option" onclick="selectChoice(this)">
                                    <span class="choice-text">冒险模式</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">选择服务器</label>
                            <div class="choice-container">
                                <div class="choice-option" onclick="selectChoice(this)">
                                    <span class="choice-text">主服务器</span>
                                </div>
                                <div class="choice-option" onclick="selectChoice(this)">
                                    <span class="choice-text">测试服务器</span>
                                </div>
                                <div class="choice-option" onclick="selectChoice(this)">
                                    <span class="choice-text">创意服务器</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">选择奖励类型</label>
                            <div class="choice-container">
                                <div class="choice-option" onclick="selectChoice(this)">
                                    <span class="choice-text">游戏道具</span>
                                </div>
                                <div class="choice-option" onclick="selectChoice(this)">
                                    <span class="choice-text">游戏币</span>
                                </div>
                                <div class="choice-option" onclick="selectChoice(this)">
                                    <span class="choice-text">特殊称号</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 抽奖按钮 -->
                        <div class="lottery-submit">
                            <button class="lottery-submit-button" id="lotterySubmitButton" onclick="handleLotterySubmit()">
                                立刻申请
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧装饰区域 -->
                <div class="lottery-decoration">
                    <!-- 右上角圆形装饰 -->
                    <div class="hero-circle"></div>
                    
                    <!-- 左上角加號 -->
                    <div class="hero-plus">+</div>
                    

                    
                    <!-- 左下角裝飾圖标 -->
                    <div class="hero-decoration">
                        <img src="/assets/images/decoration.svg" alt="裝飾" class="decoration-img">
                    </div>

                    <!-- 右下角箭頭 -->
                    <div class="hero-arrow">
                        <img src="/assets/images/arrow.svg" alt="箭頭" class="arrow-img">
                    </div>
                </div>
            </div>
        </section>
        
        <!-- BBS 论坛区域 (重构) -->
        <section class="bbs-section">
            <div class="bbs-container">
                <!-- 左侧图标墙 -->
                <div class="bbs-icon-wall">
                    <img src="/assets/images/bbs-icon-1.svg" class="bbs-icon icon-1" alt="icon">
                    <img src="/assets/images/bbs-icon-2.svg" class="bbs-icon icon-2" alt="icon">
                    <img src="/assets/images/bbs-icon-3.svg" class="bbs-icon icon-3" alt="icon">
                    <div class="bbs-icon-center" style="background-image: url('/assets/images/logo.png');"></div>
                    <img src="/assets/images/bbs-icon-4.svg" class="bbs-icon icon-4" alt="icon">
                    <img src="/assets/images/bbs-icon-5.svg" class="bbs-icon icon-5" alt="icon">
                </div>

                <!-- 右侧主要内容 -->
                <div class="bbs-main-content">
                    <div class="bbs-title-line">
                        <h2 class="bbs-title">YELOR BBS</h2>
                        <img src="/assets/images/bbs-title-icon.svg" class="bbs-title-icon" alt="title icon">
                    </div>
                    <p class="bbs-description">加入我们的社区，与其他玩家交流游戏心得</p>
                    <a href="#" class="bbs-button">
                        <span>进入论坛</span>
                        <img src="/assets/images/arrow-1-57.svg" alt="arrow" class="bbs-button-arrow">
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Pre-Footer -->
    <div class="pre-footer">
        <div class="pre-footer-container">
            <div class="pre-footer-left">
                <a href="#" class="back-to-top">
                    <img src="/assets/images/arrow.svg" alt="Back to top" class="back-to-top-icon">
                    <span>回到顶部</span>
                </a>
            </div>
            <div class="pre-footer-right">
                <!-- 右侧区域，条纹装饰通过 .pre-footer-card::after 实现 -->
            </div>
        </div>
    </div>

    <!-- Main Footer -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-logo-wrapper">
                <img src="/assets/images/logo.png" alt="YELOR Logo" class="footer-logo-img">
            </div>
            <div class="footer-links-grid">
                <!-- 左侧友情链接区域 -->
                <div class="footer-links-left">
                    <div class="footer-column">
                        <h4>友情链接</h4>
                        <ul>
                            <li><a href="#">Minecraft官网</a></li>
                            <li><a href="#">Mojang Studios</a></li>
                            <li><a href="#">Minecraft Wiki</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h4>社区</h4>
                        <ul>
                            <li><a href="#">论坛</a></li>
                            <li><a href="#">Discord</a></li>
                            <li><a href="#">QQ群</a></li>
                        </ul>
                    </div>
                    <div class="footer-column">
                        <h4>支持</h4>
                        <ul>
                            <li><a href="#">帮助中心</a></li>
                            <li><a href="#">联系我们</a></li>
                            <li><a href="#">工作</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">使用条款</a></li>
                            <li><a href="#">开发文档</a></li>
                        </ul>
                    </div>
                    <div class="footer-column empty-title">
                        <h4>&nbsp;</h4>
                        <ul>
                            <li><a href="#">赞助我们</a></li>
                            <li><a href="#">加入我们</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-info-row">
                    <span class="footer-copyright">© 2024 YELOR NETWORK. All rights reserved.</span>
                    <span class="footer-disclaimer">NOT AN OFFICIAL MINECRAFT SERVICE. NOT APPROVED BY OR ASSOCIATED WITH MOJANG OR MICROSOFT.</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- 预加载弹窗背景图片 -->
    <div class="lottery-receive-preload"></div>
    <div class="lottery-noreceive-preload"></div>

    <!-- 未登录弹窗 -->
    <div class="lottery-no-login-modal" id="lotteryNoLoginModal">
        <div class="lottery-no-login-backdrop" onclick="closeLotteryNoLoginModal()"></div>
        <div class="lottery-no-login-content">
            <div class="lottery-no-login-image">
                <button class="lottery-no-login-button" id="lotteryLoginButton" onclick="handleLoginRedirect()">
                    <span class="lottery-no-login-button-text">立刻登录</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 抽奖成功弹窗 -->
    <div class="lottery-receive-modal" id="lotteryReceiveModal">
        <div class="lottery-receive-backdrop" onclick="closeLotteryReceiveModal()"></div>
        <div class="lottery-receive-content">
            <div class="lottery-receive-image">
                <button class="lottery-receive-button" id="lotteryReceiveButton" onclick="handleReceiveConfirm()">
                    <span class="lottery-receive-button-text">确认收取</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 抽奖失败弹窗 -->
    <div class="lottery-noreceive-modal" id="lotteryNoreceiveModal">
        <div class="lottery-noreceive-backdrop" onclick="closeLotteryNoreceiveModal()"></div>
        <div class="lottery-noreceive-content">
            <div class="lottery-noreceive-image">
                <button class="lottery-noreceive-button" id="lotteryNoreceiveButton" onclick="handleRetryAttempt()">
                    <span class="lottery-noreceive-button-text">再次尝试</span>
                </button>
            </div>
        </div>
    </div>
    
    <!-- 缩放系统脚本 -->
    <script>
        function updateScaleInfo() {
            const width = window.innerWidth;
            const scaleFactor = width / 1920;
            
            // 更新CSS变量
            document.documentElement.style.setProperty('--scale-factor', scaleFactor);
        }
        
        // 页面加载时和窗口大小改变时更新
        window.addEventListener('load', updateScaleInfo);
        window.addEventListener('resize', updateScaleInfo);
        
        // 立即执行一次
        updateScaleInfo();
    </script>

    <script src="script.js"></script>

    <!-- 选择器组件JavaScript -->
    <script>
        // 选择器单选功能
        function selectChoice(selectedOption) {
            // 获取同一容器内的所有选项
            const container = selectedOption.parentElement;
            const allOptions = container.querySelectorAll('.choice-option');

            // 移除所有选项的选中状态和内联样式
            allOptions.forEach(option => {
                option.classList.remove('selected');
                // 清除可能的内联样式
                option.style.removeProperty('background-color');
                option.style.removeProperty('border-radius');
                const textElement = option.querySelector('.choice-text');
                if (textElement) {
                    textElement.style.removeProperty('color');
                    textElement.style.removeProperty('font-weight');
                }
            });

            // 为当前点击的选项添加选中状态
            selectedOption.classList.add('selected');

            // 强制应用选中样式（确保生效）
            selectedOption.style.setProperty('background-color', '#000', 'important');
            selectedOption.style.setProperty('border-radius', '21px', 'important');

            const textElement = selectedOption.querySelector('.choice-text');
            if (textElement) {
                textElement.style.setProperty('color', '#fff', 'important');
                textElement.style.setProperty('font-weight', '600', 'important');
            }

            // 可选：获取选中的值
            const selectedText = selectedOption.querySelector('.choice-text').textContent;
            console.log('选中的选项:', selectedText);

            // 可选：触发自定义事件
            const event = new CustomEvent('choiceSelected', {
                detail: {
                    container: container,
                    selectedOption: selectedOption,
                    selectedText: selectedText
                }
            });
            document.dispatchEvent(event);
        }

        // 监听选择事件（可选）
        document.addEventListener('choiceSelected', function(e) {
            console.log('选择器事件:', e.detail);
        });

        // 页面加载完成后强制添加悬停事件
        document.addEventListener('DOMContentLoaded', function() {
            console.log('开始初始化选择器悬停效果...');

            const choiceOptions = document.querySelectorAll('.choice-option');
            console.log('找到选项数量:', choiceOptions.length);

            choiceOptions.forEach((option, index) => {
                console.log('初始化选项', index + 1, ':', option.querySelector('.choice-text').textContent);

                // 鼠标进入事件
                option.addEventListener('mouseenter', function(e) {
                    console.log('悬停选项:', this.querySelector('.choice-text').textContent);

                    // 应用正式的悬停样式
                    this.style.setProperty('background-color', 'rgba(153, 153, 153, 0.2)', 'important');
                    this.style.setProperty('border-radius', '21px', 'important');
                    this.style.setProperty('transition', 'all 0.3s ease', 'important');

                    const textElement = this.querySelector('.choice-text');
                    if (textElement) {
                        textElement.style.setProperty('color', '#999', 'important'); /* 文字保持灰色 */
                    }
                });

                // 鼠标离开事件
                option.addEventListener('mouseleave', function(e) {
                    console.log('离开选项:', this.querySelector('.choice-text').textContent);

                    // 如果不是选中状态，恢复默认样式
                    if (!this.classList.contains('selected')) {
                        this.style.removeProperty('background-color');
                        this.style.removeProperty('border-radius');
                        this.style.removeProperty('transition');

                        const textElement = this.querySelector('.choice-text');
                        if (textElement) {
                            textElement.style.removeProperty('color');
                            textElement.style.removeProperty('font-weight');
                        }
                    } else {
                        // 如果是选中状态，确保选中样式保持
                        this.style.setProperty('background-color', '#000', 'important');
                        this.style.setProperty('border-radius', '21px', 'important');

                        const textElement = this.querySelector('.choice-text');
                        if (textElement) {
                            textElement.style.setProperty('color', '#fff', 'important');
                            textElement.style.setProperty('font-weight', '600', 'important');
                        }
                    }
                });

                // 添加测试用的点击事件
                option.addEventListener('click', function() {
                    console.log('🔥 点击选项:', this.querySelector('.choice-text').textContent);
                });
            });
        });
    </script>

    <!-- 抽奖系统JavaScript框架 -->
    <script>
        // ========================================
        // 抽奖系统状态管理
        // ========================================
        let lotteryState = {
            isLoggedIn: false,
            lotteryResult: null, // null, 'success', 'failure'
            buttonStatus: 'default' // 'default', 'lottery-true', 'lottery-false', 'lottery-no-login'
        };

        // ========================================
        // 调试面板功能
        // ========================================
        function updateDebugPanel() {
            document.getElementById('debugLoginStatus').textContent = lotteryState.isLoggedIn ? '已登录' : '未登录';
            document.getElementById('debugLotteryResult').textContent =
                lotteryState.lotteryResult === null ? '未抽奖' :
                lotteryState.lotteryResult === 'success' ? '成功' : '失败';

            // 更详细的按钮状态显示
            let buttonStatusText = '';
            switch(lotteryState.buttonStatus) {
                case 'ready-no-login':
                    buttonStatusText = '准备中(未登录)';
                    break;
                case 'ready-logged-in':
                    buttonStatusText = '准备中(已登录)';
                    break;
                case 'showing-no-login':
                    buttonStatusText = '显示未登录状态';
                    break;
                case 'completed-success':
                    buttonStatusText = '已完成(成功)';
                    break;
                case 'completed-failure':
                    buttonStatusText = '已完成(失败)';
                    break;
                default:
                    buttonStatusText = lotteryState.buttonStatus;
            }
            document.getElementById('debugButtonStatus').textContent = buttonStatusText;

            // 更新按钮可用性
            const successBtn = document.getElementById('successBtn');
            const failureBtn = document.getElementById('failureBtn');
            successBtn.disabled = !lotteryState.isLoggedIn;
            failureBtn.disabled = !lotteryState.isLoggedIn;
        }

        function toggleLoginStatus() {
            lotteryState.isLoggedIn = !lotteryState.isLoggedIn;
            if (!lotteryState.isLoggedIn) {
                // 登出时重置抽奖状态
                lotteryState.lotteryResult = null;
                updateButtonStatus();
            }
            updateDebugPanel();
        }

        function simulateLotterySuccess() {
            if (!lotteryState.isLoggedIn) return;
            lotteryState.lotteryResult = 'success';
            updateButtonStatus();
            updateDebugPanel();
        }

        function simulateLotteryFailure() {
            if (!lotteryState.isLoggedIn) return;
            lotteryState.lotteryResult = 'failure';
            updateButtonStatus();
            updateDebugPanel();
        }

        // ========================================
        // 按钮状态更新 - 始终显示"立刻申请"
        // ========================================
        function updateButtonStatus() {
            const button = document.getElementById('lotterySubmitButton');

            // 移除所有状态类
            button.classList.remove('lottery-true', 'lottery-false', 'lottery-no-login', 'lottery-processing');

            // 始终显示"立刻申请"，不管什么状态
            button.textContent = '立刻申请';

            // 更新调试面板的状态显示
            if (!lotteryState.isLoggedIn) {
                lotteryState.buttonStatus = 'ready-no-login';
            } else if (lotteryState.lotteryResult === 'success') {
                lotteryState.buttonStatus = 'completed-success';
            } else if (lotteryState.lotteryResult === 'failure') {
                lotteryState.buttonStatus = 'completed-failure';
            } else {
                lotteryState.buttonStatus = 'ready-logged-in';
            }
        }

        // 显示按钮状态（点击后的状态切换）
        function showButtonState(state, text, duration = 3000) {
            const button = document.getElementById('lotterySubmitButton');

            // 移除所有状态类
            button.classList.remove('lottery-true', 'lottery-false', 'lottery-no-login', 'lottery-processing');

            // 添加对应状态类和文字
            button.classList.add(state);
            button.textContent = text;

            // 如果有持续时间，则在指定时间后恢复
            if (duration > 0) {
                setTimeout(() => {
                    button.classList.remove(state);
                    button.textContent = '立刻申请';
                }, duration);
            }
        }

        // ========================================
        // 主要功能函数 - 点击时进行状态判断
        // ========================================
        function handleLotterySubmit() {
            console.log('🎲 立刻申请按钮被点击');

            // 第一步：检查登录状态
            if (!lotteryState.isLoggedIn) {
                console.log('❌ 用户未登录，显示未登录弹窗');

                // 显示未登录状态按钮样式
                showButtonState('lottery-no-login', '请先登录', 3000);
                lotteryState.buttonStatus = 'showing-no-login';
                updateDebugPanel();

                // 显示未登录弹窗
                showLotteryNoLoginModal();
                return;
            }

            // 第二步：检查是否已经抽过奖
            if (lotteryState.lotteryResult !== null) {
                console.log('⚠️ 用户已经抽过奖了');

                if (lotteryState.lotteryResult === 'success') {
                    showButtonState('lottery-true', '抽奖成功', 3000);
                } else {
                    showButtonState('lottery-false', '抽奖失败', 3000);
                }
                return;
            }

            // 第三步：显示处理中状态并开始抽奖
            console.log('✅ 用户已登录且未抽奖，开始抽奖流程');
            showButtonState('lottery-processing', '抽奖中...', 0); // 0表示不自动恢复

            // 调用抽奖API
            callLotteryAPI();
        }

        // ========================================
        // 后端API接口框架 (待实现)
        // ========================================
        async function callLotteryAPI() {
            console.log('🔄 调用抽奖API...');

            try {
                // TODO: 替换为实际的API调用
                // const response = await fetch('/api/lottery', {
                //     method: 'POST',
                //     headers: {
                //         'Content-Type': 'application/json',
                //         'Authorization': 'Bearer ' + getAuthToken()
                //     },
                //     body: JSON.stringify({
                //         userId: getCurrentUserId(),
                //         timestamp: Date.now()
                //     })
                // });
                //
                // const result = await response.json();
                //
                // if (result.success) {
                //     lotteryState.lotteryResult = 'success';
                // } else {
                //     lotteryState.lotteryResult = 'failure';
                // }

                // 临时模拟API调用
                console.log('⏳ 模拟API调用中...');
                setTimeout(() => {
                    // 50%概率成功
                    const isSuccess = Math.random() > 0.5;
                    lotteryState.lotteryResult = isSuccess ? 'success' : 'failure';

                    // 显示抽奖结果弹窗
                    if (isSuccess) {
                        console.log('🎉 抽奖成功！显示成功弹窗');
                        showLotteryReceiveModal();
                    } else {
                        console.log('😢 抽奖失败！显示失败弹窗');
                        showLotteryNoreceiveModal();
                    }

                    updateButtonStatus();
                    updateDebugPanel();
                    console.log('✅ API调用完成，结果:', lotteryState.lotteryResult);
                }, 2000); // 增加到2秒模拟网络延迟

            } catch (error) {
                console.error('❌ API调用失败:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // ========================================
        // 辅助函数 (待实现)
        // ========================================
        function getAuthToken() {
            // TODO: 从localStorage或cookie获取认证token
            return localStorage.getItem('authToken') || '';
        }

        function getCurrentUserId() {
            // TODO: 获取当前用户ID
            return localStorage.getItem('userId') || '';
        }

        function checkLoginStatus() {
            // TODO: 检查用户登录状态
            const token = getAuthToken();
            return token && token.length > 0;
        }

        // ========================================
        // 按钮鼠标悬停效果 - 从鼠标位置向外渲染蓝色
        // ========================================
        function initButtonHoverEffect() {
            const button = document.getElementById('lotterySubmitButton');

            button.addEventListener('mouseenter', function(e) {
                // 改变按钮背景颜色，保持过渡动画
                button.style.background = '#01C8FF';
                button.style.color = '#ffffff';

                // 创建渲染效果元素
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'radial-gradient(circle, rgba(1, 200, 255, 0.6) 0%, rgba(1, 200, 255, 0.3) 50%, transparent 80%)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple-expand 0.5s ease-out forwards';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '1'; // 在按钮文字上方，但透明度较低

                // 获取鼠标相对于按钮的位置
                const rect = button.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 设置渲染效果的起始位置
                const size = Math.max(button.offsetWidth, button.offsetHeight) * 2.5; // 增大尺寸确保完全覆盖
                ripple.style.width = size + 'px';
                ripple.style.height = size + 'px';
                ripple.style.left = (x - size / 2) + 'px';
                ripple.style.top = (y - size / 2) + 'px';

                // 添加到按钮中
                button.appendChild(ripple);

                // 清理函数
                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 600);
            });

            button.addEventListener('mouseleave', function() {
                // 恢复按钮原始颜色，保持过渡动画
                button.style.background = '#000000';
                button.style.color = '#ffffff';

                // 清理所有渲染效果
                const ripples = button.querySelectorAll('div');
                ripples.forEach(ripple => {
                    // 检查是否是渲染效果元素
                    if (ripple.style.position === 'absolute' && ripple.style.borderRadius === '50%') {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }
                });
            });
        }

        // ========================================
        // 未登录弹窗功能
        // ========================================
        function showLotteryNoLoginModal() {
            console.log('🚫 显示未登录弹窗');
            const modal = document.getElementById('lotteryNoLoginModal');

            if (modal) {
                // 显示弹窗
                modal.style.display = 'flex';

                // 添加显示类，触发动画
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10); // 小延迟确保display生效

                // 不禁用滚动条，避免页面宽度变化导致的卡顿
                // document.body.style.overflow = 'hidden'; // 已移除

                console.log('✅ 未登录弹窗已显示');
            } else {
                console.error('❌ 未找到弹窗元素');
            }
        }

        function closeLotteryNoLoginModal() {
            console.log('❌ 关闭未登录弹窗');
            const modal = document.getElementById('lotteryNoLoginModal');

            if (modal) {
                // 移除显示类，触发关闭动画
                modal.classList.remove('show');

                // 动画完成后隐藏弹窗
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 600); // 与CSS动画时间一致 (0.6s)

                // 不需要恢复滚动条，因为没有禁用过
                // document.body.style.overflow = ''; // 已移除

                console.log('✅ 未登录弹窗已关闭');
            }
        }

        // ========================================
        // 抽奖成功弹窗功能
        // ========================================
        function showLotteryReceiveModal() {
            console.log('🎉 显示抽奖成功弹窗');
            const modal = document.getElementById('lotteryReceiveModal');

            if (modal) {
                // 显示弹窗
                modal.style.display = 'flex';

                // 添加显示类，触发动画
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10); // 小延迟确保display生效

                console.log('✅ 抽奖成功弹窗已显示');
            } else {
                console.error('❌ 未找到抽奖成功弹窗元素');
            }
        }

        function closeLotteryReceiveModal() {
            console.log('❌ 关闭抽奖成功弹窗');
            const modal = document.getElementById('lotteryReceiveModal');

            if (modal) {
                // 移除显示类，触发关闭动画
                modal.classList.remove('show');

                // 动画完成后隐藏弹窗
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 600); // 与CSS动画时间一致 (0.6s)

                console.log('✅ 抽奖成功弹窗已关闭');
            }
        }

        // ========================================
        // 抽奖失败弹窗功能
        // ========================================
        function showLotteryNoreceiveModal() {
            console.log('😢 显示抽奖失败弹窗');
            const modal = document.getElementById('lotteryNoreceiveModal');

            if (modal) {
                // 显示弹窗
                modal.style.display = 'flex';

                // 添加显示类，触发动画
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10); // 小延迟确保display生效

                console.log('✅ 抽奖失败弹窗已显示');
            } else {
                console.error('❌ 未找到抽奖失败弹窗元素');
            }
        }

        function closeLotteryNoreceiveModal() {
            console.log('❌ 关闭抽奖失败弹窗');
            const modal = document.getElementById('lotteryNoreceiveModal');

            if (modal) {
                // 移除显示类，触发关闭动画
                modal.classList.remove('show');

                // 动画完成后隐藏弹窗
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 600); // 与CSS动画时间一致 (0.6s)

                console.log('✅ 抽奖失败弹窗已关闭');
            }
        }

        // ========================================
        // 按钮功能实现
        // ========================================
        function handleReceiveConfirm() {
            console.log('🎁 用户确认收取奖励');

            // 关闭抽奖成功弹窗
            closeLotteryReceiveModal();

            // 预留奖励发放接口
            // TODO: 调用后端API发放奖励
            // await claimReward(lotteryState.rewardId);

            console.log('✅ 奖励确认收取完成');
        }

        function handleRetryAttempt() {
            console.log('🔄 用户选择再次尝试');

            // 关闭抽奖失败弹窗
            closeLotteryNoreceiveModal();

            // 重置抽奖状态，允许再次抽奖
            lotteryState.lotteryResult = null;
            updateButtonStatus();
            updateDebugPanel();

            console.log('✅ 抽奖状态已重置，可以再次尝试');
        }

        // ========================================
        // 登录按钮功能
        // ========================================
        function handleLoginRedirect() {
            console.log('🔑 用户点击登录按钮');

            // 关闭弹窗
            closeLotteryNoLoginModal();

            // 预留登录页面跳转接口
            // TODO: 替换为实际的登录页面URL
            const loginUrl = '/login.html'; // 预留接口

            console.log('🔄 准备跳转到登录页面:', loginUrl);

            // 跳转到登录页面
            // window.location.href = loginUrl;

            // 临时提示（开发阶段）
            alert('跳转到登录页面功能预留\n登录页面URL: ' + loginUrl);
        }

        // ESC键关闭弹窗 - 支持所有弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // 检查并关闭当前显示的弹窗
                const noLoginModal = document.getElementById('lotteryNoLoginModal');
                const receiveModal = document.getElementById('lotteryReceiveModal');
                const noreceiveModal = document.getElementById('lotteryNoreceiveModal');

                if (noLoginModal && noLoginModal.classList.contains('show')) {
                    closeLotteryNoLoginModal();
                } else if (receiveModal && receiveModal.classList.contains('show')) {
                    closeLotteryReceiveModal();
                } else if (noreceiveModal && noreceiveModal.classList.contains('show')) {
                    closeLotteryNoreceiveModal();
                }
            }
        });

        // 初始化抽奖成功按钮悬停效果
        function initReceiveButtonHoverEffect() {
            const receiveButton = document.getElementById('lotteryReceiveButton');

            if (!receiveButton) return;

            receiveButton.addEventListener('mouseenter', function(e) {
                receiveButton.style.background = '#01C8FF';
                receiveButton.style.color = '#ffffff';

                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'radial-gradient(circle, rgba(1, 200, 255, 0.6) 0%, rgba(1, 200, 255, 0.3) 50%, transparent 80%)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple-expand 0.5s ease-out forwards';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '1';

                const rect = receiveButton.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const size = Math.max(receiveButton.offsetWidth, receiveButton.offsetHeight) * 2.5;
                ripple.style.width = size + 'px';
                ripple.style.height = size + 'px';
                ripple.style.left = (x - size / 2) + 'px';
                ripple.style.top = (y - size / 2) + 'px';

                receiveButton.appendChild(ripple);

                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 500);
            });

            receiveButton.addEventListener('mouseleave', function() {
                receiveButton.style.background = '#000000';
                receiveButton.style.color = '#ffffff';

                const ripples = receiveButton.querySelectorAll('div');
                ripples.forEach(ripple => {
                    if (ripple.style.position === 'absolute' && ripple.style.borderRadius === '50%') {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }
                });
            });
        }

        // 初始化抽奖失败按钮悬停效果
        function initNoreceiveButtonHoverEffect() {
            const noreceiveButton = document.getElementById('lotteryNoreceiveButton');

            if (!noreceiveButton) return;

            noreceiveButton.addEventListener('mouseenter', function(e) {
                noreceiveButton.style.background = '#01C8FF';
                noreceiveButton.style.color = '#ffffff';

                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'radial-gradient(circle, rgba(1, 200, 255, 0.6) 0%, rgba(1, 200, 255, 0.3) 50%, transparent 80%)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple-expand 0.5s ease-out forwards';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '1';

                const rect = noreceiveButton.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const size = Math.max(noreceiveButton.offsetWidth, noreceiveButton.offsetHeight) * 2.5;
                ripple.style.width = size + 'px';
                ripple.style.height = size + 'px';
                ripple.style.left = (x - size / 2) + 'px';
                ripple.style.top = (y - size / 2) + 'px';

                noreceiveButton.appendChild(ripple);

                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 500);
            });

            noreceiveButton.addEventListener('mouseleave', function() {
                noreceiveButton.style.background = '#000000';
                noreceiveButton.style.color = '#ffffff';

                const ripples = noreceiveButton.querySelectorAll('div');
                ripples.forEach(ripple => {
                    if (ripple.style.position === 'absolute' && ripple.style.borderRadius === '50%') {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }
                });
            });
        }

        // 初始化登录按钮悬停效果
        function initLoginButtonHoverEffect() {
            const loginButton = document.getElementById('lotteryLoginButton');

            if (!loginButton) return;

            loginButton.addEventListener('mouseenter', function(e) {
                // 改变按钮背景颜色，保持过渡动画
                loginButton.style.background = '#01C8FF';
                loginButton.style.color = '#ffffff';

                // 创建渲染效果元素
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'radial-gradient(circle, rgba(1, 200, 255, 0.6) 0%, rgba(1, 200, 255, 0.3) 50%, transparent 80%)';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple-expand 0.5s ease-out forwards';
                ripple.style.pointerEvents = 'none';
                ripple.style.zIndex = '1';

                // 获取鼠标相对于按钮的位置
                const rect = loginButton.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 设置渲染效果的起始位置
                const size = Math.max(loginButton.offsetWidth, loginButton.offsetHeight) * 2.5;
                ripple.style.width = size + 'px';
                ripple.style.height = size + 'px';
                ripple.style.left = (x - size / 2) + 'px';
                ripple.style.top = (y - size / 2) + 'px';

                // 添加到按钮中
                loginButton.appendChild(ripple);

                // 清理函数
                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 500);
            });

            loginButton.addEventListener('mouseleave', function() {
                // 恢复按钮原始颜色，保持过渡动画
                loginButton.style.background = '#000000';
                loginButton.style.color = '#ffffff';

                // 清理所有渲染效果
                const ripples = loginButton.querySelectorAll('div');
                ripples.forEach(ripple => {
                    // 检查是否是渲染效果元素
                    if (ripple.style.position === 'absolute' && ripple.style.borderRadius === '50%') {
                        if (ripple.parentNode) {
                            ripple.parentNode.removeChild(ripple);
                        }
                    }
                });
            });
        }

        // ========================================
        // 页面初始化
        // ========================================
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 抽奖系统初始化...');

            // 检查登录状态
            lotteryState.isLoggedIn = checkLoginStatus();

            // 初始化所有按钮悬停效果
            initButtonHoverEffect();
            initLoginButtonHoverEffect();
            initReceiveButtonHoverEffect();
            initNoreceiveButtonHoverEffect();

            // 更新UI
            updateButtonStatus();
            updateDebugPanel();

            console.log('✅ 抽奖系统初始化完成');
        });
    </script>
</body>
</html>
