/* 抽奖页面专用样式 */

/* 抽奖页面的hero区域调整 */
.lottery-page .hero-section {
    /* 为后续的抽奖内容预留空间 */
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* 确保装饰元素在没有logo时仍然正常显示 */
.lottery-page .hero-plus,
.lottery-page .hero-decoration,
.lottery-page .hero-arrow {
    /* 保持原有样式 */
}

/* 为后续添加抽奖相关内容预留的样式区域 */
.lottery-content {
    /* 这里将来会添加抽奖界面的样式 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 10;
}

/* 抽奖页面的响应式调整 */
@media (max-width: 768px) {
    .lottery-page .hero-section {
        min-height: 400px;
    }
}

/* 抽奖页面的hero区域调整 */
.lottery-page .hero-section {
    /* 为后续的抽奖内容预留空间 */
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* 确保装饰元素在没有logo时仍然正常显示 */
.lottery-page .hero-plus,
.lottery-page .hero-decoration,
.lottery-page .hero-arrow {
    /* 保持原有样式 */
}

/* 为后续添加抽奖相关内容预留的样式区域 */
.lottery-content {
    /* 这里将来会添加抽奖界面的样式 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 10;
}

/* 抽奖页面的响应式调整 */
@media (max-width: 768px) {
    .lottery-page .hero-section {
        min-height: 400px;
    }
}
