/* 登录界面 (login-plant) 专用样式文件 */

/* 登录界面图片区域 - 位置由login-layout.css管理 */
.login-page .hero-section .login-image-section,
.hero-section .login-image-section {
    /* 位置属性由login-layout.css管理 */
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* 登录背景图片样式 - 尺寸由login-layout.css管理 */
.login-page .hero-section .login-bg,
.hero-section .login-bg {
    /* 尺寸属性由login-layout.css管理 */
    width: auto;
    height: auto;
    object-fit: contain;
}

/* ========================================
   登录输入框样式
   ======================================== */
.login-input {
    width: 230px !important;
    height: 25px !important;
    border: none !important;                    /* 移除所有边框 */
    border-bottom: 2px solid #ddd !important;  /* 只保留底部边框 */
    border-radius: 0 !important;               /* 移除圆角 */
    padding: 8px 12px !important;
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 14px !important;
    color: #333 !important;
    background-color: transparent !important;   /* 背景透明 */
    transition: all 0.3s ease !important;
    outline: none !important;
    box-sizing: border-box !important;
}

.login-input:focus {
    border-bottom-color: #01C8FF !important;   /* 只改变底部边框颜色 */
    box-shadow: 0 2px 0 0 rgba(1, 200, 255, 0.3) !important; /* 底部阴影效果 */
}

.login-input::placeholder {
    color: #666 !important;                    /* 稍微深一点的placeholder颜色 */
    font-size: 13px !important;
    opacity: 0.8 !important;                   /* 增加透明度 */
}

/* 输入框悬停效果 */
.login-input:hover {
    border-bottom-color: #999 !important;     /* 悬停时底部边框变深 */
}

/* 复选框样式 */
.checkbox-container {
    display: flex !important;
    align-items: center !important;
    cursor: pointer !important;
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 14px !important;
    color: #333 !important;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 8px !important;
    width: 16px !important;
    height: 16px !important;
    accent-color: #01C8FF !important;
}

.checkbox-container .checkmark {
    user-select: none !important;
}

/* 登录内容覆盖层 */
.login-page .hero-section .questionnaire-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 25;
    pointer-events: none; /* 允许点击穿透到图片 */
}

/* 为第2、3、4、5区域启用鼠标事件 */
.login-page .hero-section .questionnaire-overlay .section.section-2,
.login-page .hero-section .questionnaire-overlay .section.section-3,
.login-page .hero-section .questionnaire-overlay .section.section-4,
.login-page .hero-section .questionnaire-overlay .section.section-5 {
    pointer-events: auto; /* 启用区域的鼠标事件 */
}

/* 每个区域占据20%的高度 */
.login-page .hero-section .questionnaire-overlay .section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-left: 40px;
    padding-right: 200px; /* 为右侧选项按钮预留空间 */
    box-sizing: border-box;
}

/* 重写第2、3、4区域对齐 - 精确垂直对齐 */
.login-page .hero-section .questionnaire-overlay .section-2,
.login-page .hero-section .questionnaire-overlay .section-3,
.login-page .hero-section .questionnaire-overlay .section-4 {
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch !important; /* 改为stretch，让子元素高度一致 */
    justify-content: flex-start !important;
    padding-left: 60px !important; /* 统一的左边距，确保#号对齐 */
    padding-right: 200px !important;
    box-sizing: border-box !important;
    gap: 0 !important; /* 重置gap，使用固定定位 */
    min-height: 80px !important; /* 设置最小高度确保对齐 */
}

/* 单独设置用户名区域 (section-2) */
.login-page .hero-section .questionnaire-overlay .section-2 {
    margin-top: 210px !important;      /* 向下移动用户名框 */
    min-height: 100px !important;     /* 用户名区域高度 */
    margin-bottom: 10px !important;   /* 与密码框的间隔 */
}

/* 单独设置密码区域 (section-3) */
.login-page .hero-section .questionnaire-overlay .section-3 {
    min-height: 100px !important;     /* 密码区域高度 */
    margin-bottom: 20px !important;   /* 与记住我的间隔 */
}

/* 单独设置记住我区域 (section-4) */
.login-page .hero-section .questionnaire-overlay .section-4 {
    margin-top: 30px !important;      /* 向下移动记住我 */
    min-height: 80px !important;      /* 记住我区域高度 */
    margin-bottom: 15px !important;   /* 与登录按钮的间隔 */
}

/* 单独设置登录按钮区域 (section-5) - 恢复原始位置 */
.login-page .hero-section .questionnaire-overlay .section-5 {
    margin-top: 0px !important;       /* 重置顶部边距 */
    margin-bottom: 0px !important;    /* 重置底部边距 */
    flex: 1 !important;               /* 保持原有的flex设置 */
    display: flex !important;         /* 保持flex布局 */
    justify-content: center !important; /* 保持居中对齐 */
    align-items: center !important;   /* 保持垂直居中 */
    pointer-events: auto !important;  /* 确保区域可以接收鼠标事件 */
    z-index: 999 !important;          /* 确保区域在上层 */
    position: relative !important;    /* 确保z-index生效 */
}

/* 问题内容区域 - 精确高度匹配选择器 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-content,
.login-page .hero-section .questionnaire-overlay .section-3 .question-content,
.login-page .hero-section .questionnaire-overlay .section-4 .question-content {
    width: 200px !important; /* 固定宽度 */
    height: 80px !important; /* 与选择器高度完全一致 */
    flex-shrink: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important; /* 顶部和底部对齐 */
    align-items: flex-start !important;
    box-sizing: border-box !important;
}

/* 选择器容器 - 精确高度匹配问题内容 */
.login-page .hero-section .questionnaire-overlay .section-2 .choice-container,
.login-page .hero-section .questionnaire-overlay .section-3 .choice-container,
.login-page .hero-section .questionnaire-overlay .section-4 .choice-container {
    margin-left: -205px !important; /* 固定间距替代gap */
    width: 150px !important;
    height: 80px !important; /* 与问题内容高度完全一致 */
    flex-shrink: 0 !important;
    pointer-events: auto !important;
    z-index: 100 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important; /* 确保第一个和第三个选项对齐边缘 */
}

/* 问题编号 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-number,
.login-page .hero-section .questionnaire-overlay .section-3 .question-number,
.login-page .hero-section .questionnaire-overlay .section-4 .question-number {
    display: none !important;
}

/* 问题文字容器 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-lines,
.login-page .hero-section .questionnaire-overlay .section-3 .question-lines,
.login-page .hero-section .questionnaire-overlay .section-4 .question-lines {
    display: none !important;
}

/* 问题文字行 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-line,
.login-page .hero-section .questionnaire-overlay .section-3 .question-line,
.login-page .hero-section .questionnaire-overlay .section-4 .question-line {
    display: none !important;
}

/* 最后一个问题行的特殊处理 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-line:last-child,
.login-page .hero-section .questionnaire-overlay .section-3 .question-line:last-child,
.login-page .hero-section .questionnaire-overlay .section-4 .question-line:last-child {
    margin-bottom: 0 !important; /* 确保底部对齐 */
}

/* 标题样式 (第1区域) - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .section-1 .title-text {
    display: none !important;
}

/* 问题编号样式 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .question-number {
    display: none !important;
}

/* 问题文本样式 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .question-line {
    display: none !important;
}

/* ========================================
   按钮状态管理系统
   ======================================== */
/* 基础提交按钮样式 - 使用多重选择器确保优先级 */
html body .login-page .hero-section .questionnaire-overlay .submit-button,
body .login-page .hero-section .questionnaire-overlay .submit-button,
.login-page .hero-section .questionnaire-overlay .submit-button,
#loginSubmitButton {
    /* 基础样式 */
    width: 150px !important;
    height: 40px !important;
    border-radius: 40px !important;
    background-color: #000000 !important;
    color: #ffffff !important;
    border: none !important;
    outline: none !important; /* 确保没有焦点边框 */
    cursor: pointer !important;
    pointer-events: auto !important; /* 确保按钮可以接收鼠标事件 */
    z-index: 1000 !important; /* 确保按钮在最上层 */
    overflow: hidden !important; /* 确保ripple效果不会超出按钮边界 */
    position: relative !important; /* 确保ripple效果正确定位 */

    /* 位置控制 - 重置按钮位置 */
    position: relative !important;
    top: -75px !important;       /* 重置垂直位置偏移 */
    left: -130px !important;      /* 重置水平位置偏移 */
    margin: 0 auto !important; /* 水平居中 */

    /* 文字样式 */
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 16px !important;
    font-weight: 500 !important;

    /* 交互效果 - 与lottery完全相同 */
    transition: all 0.3s ease !important;
    user-select: none !important;
    overflow: hidden !important;

    /* 居中对齐 */
    display: flex;
    align-items: center;
    justify-content: center;

    /* 定位 */
    margin: 0 auto;
}

/* 鼠标悬停效果 - 与lottery完全相同的CSS hover效果 */
html body .login-page .hero-section .questionnaire-overlay .submit-button:hover,
body .login-page .hero-section .questionnaire-overlay .submit-button:hover,
.login-page .hero-section .questionnaire-overlay .submit-button:hover,
#loginSubmitButton:hover {
    background: #01C8FF !important;
    color: #ffffff !important;
}

/* 第五区域 - 提交按钮 */
.login-page .hero-section .questionnaire-overlay .section-5 {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    pointer-events: auto; /* 启用按钮区域的鼠标事件 */
}

/* 提交按钮样式 - 与lottery完全一致 */
.login-page .hero-section .questionnaire-overlay .section-5 .submit-button {
    /* 基础样式 - 与lottery相同 */
    width: 150px !important;
    height: 40px !important;
    border-radius: 40px !important;
    background-color: #000000 !important;
    color: #ffffff !important;
    border: none !important;
    outline: none !important;
    cursor: pointer !important;
    pointer-events: auto !important;
    z-index: 1001 !important;

    /* 文字样式 - 与lottery相同 */
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 16px !important;
    font-weight: 500 !important;

    /* 交互效果 - 与lottery相同 */
    transition: all 0.3s ease !important;
    user-select: none !important;
    overflow: hidden !important;

    /* 居中对齐 */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    /* 定位 */
    position: relative !important;
}

/* section-5按钮的悬停效果 - 与lottery完全相同 */
.login-page .hero-section .questionnaire-overlay .section-5 .submit-button:hover {
    background: #01C8FF !important;
    color: #ffffff !important;
}

/* 渲染动画关键帧 - 与lottery按钮相同的效果 */
@keyframes ripple-expand {
    0% {
        transform: scale(0);
        opacity: 0.8;
    }
    50% {
        opacity: 0.4;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}
