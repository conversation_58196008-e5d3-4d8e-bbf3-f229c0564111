/* 登录界面 (login-plant) 专用样式文件 */

/* 登录界面图片区域 - 位置由login-layout.css管理 */
.login-page .hero-section .login-image-section,
.hero-section .login-image-section {
    /* 位置属性由login-layout.css管理 */
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* 登录背景图片样式 - 尺寸由login-layout.css管理 */
.login-page .hero-section .login-bg,
.hero-section .login-bg {
    /* 尺寸属性由login-layout.css管理 */
    width: auto;
    height: auto;
    object-fit: contain;
}

/* ========================================
   登录输入框样式
   ======================================== */
.login-input {
    width: 150px !important;
    height: 25px !important;
    border: 2px solid #ddd !important;
    border-radius: 8px !important;
    padding: 8px 12px !important;
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 14px !important;
    color: #333 !important;
    background-color: #fff !important;
    transition: all 0.3s ease !important;
    outline: none !important;
    box-sizing: border-box !important;
}

.login-input:focus {
    border-color: #01C8FF !important;
    box-shadow: 0 0 0 3px rgba(1, 200, 255, 0.1) !important;
}

.login-input::placeholder {
    color: #999 !important;
    font-size: 13px !important;
}

/* 复选框样式 */
.checkbox-container {
    display: flex !important;
    align-items: center !important;
    cursor: pointer !important;
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 14px !important;
    color: #333 !important;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 8px !important;
    width: 16px !important;
    height: 16px !important;
    accent-color: #01C8FF !important;
}

.checkbox-container .checkmark {
    user-select: none !important;
}

/* 登录内容覆盖层 */
.login-page .hero-section .questionnaire-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 25;
    pointer-events: none; /* 允许点击穿透到图片 */
}

/* 为第2、3、4区域启用鼠标事件 */
.login-page .hero-section .questionnaire-overlay .section.section-2,
.login-page .hero-section .questionnaire-overlay .section.section-3,
.login-page .hero-section .questionnaire-overlay .section.section-4 {
    pointer-events: auto; /* 启用区域的鼠标事件 */
}

/* 每个区域占据20%的高度 */
.login-page .hero-section .questionnaire-overlay .section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-left: 40px;
    padding-right: 200px; /* 为右侧选项按钮预留空间 */
    box-sizing: border-box;
}

/* 重写第2、3、4区域对齐 - 精确垂直对齐 */
.login-page .hero-section .questionnaire-overlay .section-2,
.login-page .hero-section .questionnaire-overlay .section-3,
.login-page .hero-section .questionnaire-overlay .section-4 {
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch !important; /* 改为stretch，让子元素高度一致 */
    justify-content: flex-start !important;
    padding-left: 60px !important; /* 统一的左边距，确保#号对齐 */
    padding-right: 200px !important;
    box-sizing: border-box !important;
    gap: 0 !important; /* 重置gap，使用固定定位 */
    min-height: 80px !important; /* 设置最小高度确保对齐 */
}

/* 问题内容区域 - 精确高度匹配选择器 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-content,
.login-page .hero-section .questionnaire-overlay .section-3 .question-content,
.login-page .hero-section .questionnaire-overlay .section-4 .question-content {
    width: 200px !important; /* 固定宽度 */
    height: 80px !important; /* 与选择器高度完全一致 */
    flex-shrink: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important; /* 顶部和底部对齐 */
    align-items: flex-start !important;
    box-sizing: border-box !important;
}

/* 选择器容器 - 精确高度匹配问题内容 */
.login-page .hero-section .questionnaire-overlay .section-2 .choice-container,
.login-page .hero-section .questionnaire-overlay .section-3 .choice-container,
.login-page .hero-section .questionnaire-overlay .section-4 .choice-container {
    margin-left: 80px !important; /* 固定间距替代gap */
    width: 150px !important;
    height: 80px !important; /* 与问题内容高度完全一致 */
    flex-shrink: 0 !important;
    pointer-events: auto !important;
    z-index: 100 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important; /* 确保第一个和第三个选项对齐边缘 */
}

/* 问题编号 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-number,
.login-page .hero-section .questionnaire-overlay .section-3 .question-number,
.login-page .hero-section .questionnaire-overlay .section-4 .question-number {
    display: none !important;
}

/* 问题文字容器 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-lines,
.login-page .hero-section .questionnaire-overlay .section-3 .question-lines,
.login-page .hero-section .questionnaire-overlay .section-4 .question-lines {
    display: none !important;
}

/* 问题文字行 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-line,
.login-page .hero-section .questionnaire-overlay .section-3 .question-line,
.login-page .hero-section .questionnaire-overlay .section-4 .question-line {
    display: none !important;
}

/* 最后一个问题行的特殊处理 */
.login-page .hero-section .questionnaire-overlay .section-2 .question-line:last-child,
.login-page .hero-section .questionnaire-overlay .section-3 .question-line:last-child,
.login-page .hero-section .questionnaire-overlay .section-4 .question-line:last-child {
    margin-bottom: 0 !important; /* 确保底部对齐 */
}

/* 标题样式 (第1区域) - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .section-1 .title-text {
    display: none !important;
}

/* 问题编号样式 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .question-number {
    display: none !important;
}

/* 问题文本样式 - 隐藏文字 */
.login-page .hero-section .questionnaire-overlay .question-line {
    display: none !important;
}

/* ========================================
   按钮状态管理系统
   ======================================== */
/* 基础提交按钮样式 - 使用多重选择器确保优先级 */
html body .login-page .hero-section .questionnaire-overlay .submit-button,
body .login-page .hero-section .questionnaire-overlay .submit-button,
.login-page .hero-section .questionnaire-overlay .submit-button,
#loginSubmitButton {
    /* 基础样式 */
    width: 150px !important;
    height: 40px !important;
    border-radius: 40px !important;
    background-color: #000000 !important;
    color: #ffffff !important;
    border: none !important;
    outline: none !important; /* 确保没有焦点边框 */
    cursor: pointer !important;

    /* 位置控制 - 可以修改这些值来移动按钮 */
    position: relative !important;
    top: -4px !important;    /* 垂直位置偏移 */
    left: -130px !important;   /* 水平位置偏移 */
    margin: 0 auto !important; /* 水平居中 */

    /* 文字样式 */
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 16px !important;
    font-weight: 500 !important;

    /* 交互效果 */
    transition: all 0.3s ease;
    user-select: none;
    overflow: hidden !important;

    /* 居中对齐 */
    display: flex;
    align-items: center;
    justify-content: center;

    /* 定位 */
    margin: 0 auto;
}

/* 鼠标悬停效果 - 从鼠标位置向外渲染蓝色 */
html body .login-page .hero-section .questionnaire-overlay .submit-button:hover,
body .login-page .hero-section .questionnaire-overlay .submit-button:hover,
.login-page .hero-section .questionnaire-overlay .submit-button:hover,
#loginSubmitButton:hover {
    background: #01C8FF !important;
    color: #ffffff !important;
}

/* 第五区域 - 提交按钮 */
.login-page .hero-section .questionnaire-overlay .section-5 {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    pointer-events: auto; /* 启用按钮区域的鼠标事件 */
}

/* 提交按钮样式 */
.login-page .hero-section .questionnaire-overlay .section-5 .submit-button {
    /* CSS变量 - 便于后续调整 */
    --button-width: 200px;
    --button-height: 50px;
    --button-border-radius: 21px;
    --button-font-size: 18px;

    /* 基础样式 */
    width: var(--button-width);
    height: var(--button-height);
    border-radius: var(--button-border-radius);
    background-color: #000000;
    color: #ffffff;
    border: none;
    cursor: pointer;

    /* 文字样式 */
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: var(--button-font-size);
    font-weight: 500;

    /* 交互效果 */
    transition: all 0.3s ease;
    user-select: none;

    /* 居中对齐 */
    display: flex;
    align-items: center;
    justify-content: center;
}
