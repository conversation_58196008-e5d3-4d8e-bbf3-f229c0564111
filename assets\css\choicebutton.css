/* 选择器组件 (Choice Button) CSS */

/* 主容器 - 垂直排列三个选项，精确对齐 */
.choice-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    gap: 0; /* 选项之间的间距，可调整 */
    justify-content: space-between; /* 第一个选项顶部对齐，第三个选项底部对齐 */
    align-items: stretch; /* 选项宽度填满容器 */
    box-sizing: border-box;
}

/* 单个选项 - 精确高度分布 */
.choice-option {
    flex: 1; /* 平分容器高度 */
    display: flex;
    align-items: center;
    padding: 2px 6px; /* 最小内边距确保对齐精度 - 可调整 */
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease; /* 动画过渡时间 - 可调整 */
    border-radius: 6px; /* 稍小的圆角 - 可调整 */
    margin: 0; /* 移除边距确保精确对齐 */
    box-sizing: border-box;
    min-height: 0; /* 允许收缩到最小尺寸 */
}

/* 圆形图标样式 */
.choice-option::before {
    content: '';
    width: 16px;              /* 圆圈大小 - 可调整 */
    height: 16px;             /* 圆圈大小 - 可调整 */
    border: 2px solid #999;   /* 边框粗细和颜色 - 可调整 */
    border-radius: 50%;       /* 保持圆形 */
    margin-right: 12px;       /* 圆圈与文字间距 - 可调整 */
    flex-shrink: 0;           /* 防止圆圈被压缩 */
    transition: all 0.3s ease;
}

/* 选项文字 */
.choice-option .choice-text {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-size: 16px;          /* 文字大小 - 可调整 */
    font-weight: 500;         /* 文字粗细 - 可调整 */
    color: #999;              /* 默认文字颜色（灰色）- 可调整 */
    transition: all 0.3s ease; /* 增强过渡效果 */
    line-height: 1.4;         /* 行高 - 可调整 */
}

/* 通用悬停效果 - 备用方案 */
.choice-option:hover {
    background-color: rgba(153, 153, 153, 0.25) !important;
    border-radius: 6px !important;
}

.choice-option:hover::before {
    border-color: #555 !important;
}

.choice-option:hover .choice-text {
    color: #333 !important;
}

/* 悬停状态 - 最高优先级 */
.lottery-page .hero-section .questionnaire-overlay .choice-container .choice-option:hover {
    background-color: rgba(153, 153, 153, 0.3) !important; /* 悬停背景色（更明显的灰色半透明）- 可调整 */
    transform: scale(1.02) !important; /* 轻微放大效果 */
}

.lottery-page .hero-section .questionnaire-overlay .choice-container .choice-option:hover::before {
    border-color: #666 !important;       /* 悬停时圆圈边框颜色 - 可调整 */
    background-color: rgba(102, 102, 102, 0.1) !important; /* 圆圈内部轻微填充 */
}

.lottery-page .hero-section .questionnaire-overlay .choice-container .choice-option:hover .choice-text {
    color: #333 !important;              /* 悬停时文字颜色（更深的灰色）- 可调整 */
    font-weight: 600 !important;         /* 悬停时文字加粗 */
}

/* 选中状态 - 超高优先级 */
body .choice-option.selected,
html body .choice-option.selected,
.lottery-page .choice-option.selected,
.hero-section .choice-option.selected,
.questionnaire-overlay .choice-option.selected,
.choice-container .choice-option.selected {
    background-color: #000 !important;   /* 选中背景色（黑色）*/
    border-radius: 21px !important;      /* 与悬停状态相同的圆角 */
    color: #fff !important;
}

body .choice-option.selected::before,
html body .choice-option.selected::before,
.lottery-page .choice-option.selected::before,
.hero-section .choice-option.selected::before,
.questionnaire-overlay .choice-option.selected::before,
.choice-container .choice-option.selected::before {
    border-color: #fff !important;       /* 选中时圆圈边框颜色（白色）*/
    background-color: #000 !important;   /* 选中时圆圈填充颜色（黑色）*/
}

body .choice-option.selected .choice-text,
html body .choice-option.selected .choice-text,
.lottery-page .choice-option.selected .choice-text,
.hero-section .choice-option.selected .choice-text,
.questionnaire-overlay .choice-option.selected .choice-text,
.choice-container .choice-option.selected .choice-text {
    color: #fff !important;              /* 选中时文字颜色（白色）*/
    font-weight: 600 !important;         /* 选中时文字加粗 */
}

/* 选中状态的蓝色装饰元素 */
body .choice-option.selected::after,
html body .choice-option.selected::after,
.lottery-page .choice-option.selected::after,
.hero-section .choice-option.selected::after,
.questionnaire-overlay .choice-option.selected::after,
.choice-container .choice-option.selected::after {
    content: '';
    position: absolute;
    right: 8px;                          /* 距离右边缘8px - 可调整 */
    top: 50%;
    transform: translateY(-50%);         /* 垂直居中 */
    width: 12px;                         /* 蓝色矩形宽度 - 可调整 */
    height: 8px;                         /* 蓝色矩形高度（圆圈高度的一半）- 可调整 */
    background-color: #1D5FFF !important; /* 蓝色 - 可调整 */
    border-radius: 21px !important;     /* 圆角21px */
    z-index: 10;
}

/* 选中状态悬停时保持样式 */
body .choice-option.selected:hover,
html body .choice-option.selected:hover,
.lottery-page .choice-option.selected:hover,
.hero-section .choice-option.selected:hover,
.questionnaire-overlay .choice-option.selected:hover,
.choice-container .choice-option.selected:hover {
    background-color: #333 !important;   /* 选中状态悬停时的背景色 */
}

/* 选中状态悬停时蓝色装饰元素保持显示 */
body .choice-option.selected:hover::after,
html body .choice-option.selected:hover::after,
.lottery-page .choice-option.selected:hover::after,
.hero-section .choice-option.selected:hover::after,
.questionnaire-overlay .choice-option.selected:hover::after,
.choice-container .choice-option.selected:hover::after {
    background-color: #1D5FFF !important; /* 悬停时蓝色装饰保持蓝色 */
}

/* 响应式设计 - 中等屏幕 */
@media (max-width: 1200px) {
    .choice-option {
        padding: 3px 6px;     /* 中等屏幕内边距 - 可调整 */
    }

    .choice-option::before {
        width: 14px;          /* 中等屏幕圆圈大小 - 可调整 */
        height: 14px;
        margin-right: 10px;   /* 中等屏幕圆圈与文字间距 - 可调整 */
    }

    .choice-option .choice-text {
        font-size: 15px;      /* 中等屏幕文字大小 - 可调整 */
    }

    /* 中等屏幕蓝色装饰元素调整 */
    .choice-option.selected::after {
        width: 10px;          /* 中等屏幕蓝色矩形宽度 - 可调整 */
        height: 7px;          /* 中等屏幕蓝色矩形高度 - 可调整 */
        right: 6px;           /* 中等屏幕距离右边缘 - 可调整 */
    }
}

/* 响应式设计 - 移动端 */
@media (max-width: 768px) {
    .choice-option {
        padding: 2px 4px;     /* 移动端内边距 - 可调整 */
    }

    .choice-option::before {
        width: 12px;          /* 移动端圆圈大小 - 可调整 */
        height: 12px;
        margin-right: 8px;    /* 移动端圆圈与文字间距 - 可调整 */
    }

    .choice-option .choice-text {
        font-size: 14px;      /* 移动端文字大小 - 可调整 */
    }

    /* 移动端蓝色装饰元素调整 */
    .choice-option.selected::after {
        width: 8px;           /* 移动端蓝色矩形宽度 - 可调整 */
        height: 6px;          /* 移动端蓝色矩形高度 - 可调整 */
        right: 4px;           /* 移动端距离右边缘 - 可调整 */
    }
}

/* 超高优先级悬停效果 - 正式版本 */
body .choice-option:hover,
html body .choice-option:hover,
.lottery-page .choice-option:hover,
.hero-section .choice-option:hover,
.questionnaire-overlay .choice-option:hover,
.choice-container .choice-option:hover {
    background-color: rgba(153, 153, 153, 0.2) !important; /* 灰色半透明背景 */
    border-radius: 21px !important; /* 圆角弧度改为21px */
    transition: all 0.3s ease !important;
}

body .choice-option:hover .choice-text,
html body .choice-option:hover .choice-text,
.lottery-page .choice-option:hover .choice-text,
.hero-section .choice-option:hover .choice-text,
.questionnaire-overlay .choice-option:hover .choice-text,
.choice-container .choice-option:hover .choice-text {
    color: #999 !important; /* 悬停时文字保持灰色 */
}

body .choice-option:hover::before,
html body .choice-option:hover::before,
.lottery-page .choice-option:hover::before,
.hero-section .choice-option:hover::before,
.questionnaire-overlay .choice-option:hover::before,
.choice-container .choice-option:hover::before {
    border-color: #666 !important; /* 悬停时圆圈边框稍深 */
}

/*
自定义调整指南：

1. 圆圈样式调整：
   - 大小：修改 width 和 height (第25-26行)
   - 边框粗细：修改 border 的像素值 (第27行)
   - 边框颜色：修改 border 的颜色值 (第27行)
   - 与文字间距：修改 margin-right (第29行)

2. 文字样式调整：
   - 字体大小：修改 font-size (第36行)
   - 字体粗细：修改 font-weight (第37行)
   - 默认颜色：修改 color (第38行)
   - 行高：修改 line-height (第40行)

3. 背景和间距调整：
   - 选项内边距：修改 padding (第18行)
   - 选项间距：修改 gap (第11行) 或 margin (第22行)
   - 圆角大小：修改 border-radius (第21行)
   - 悬停背景：修改 background-color (第44行)
   - 选中背景：修改 background-color (第54行)

4. 动画效果调整：
   - 过渡时间：修改 transition 的时间值 (第20行)
   - 过渡效果：修改 transition 的 ease 类型 (第20行)
*/
