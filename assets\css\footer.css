/* 页脚样式 - Footer Styles */

/* Pre-footer 区域 */
.pre-footer {
    background-color: #d9d9d9;
    padding: 60px 20px;
    border-top: 1px solid #000;
    position: relative;
}

.pre-footer-card {
    position: relative;
    max-width: 859px;
    height: 260px;
    margin: 0 auto;
    background-color: #ffffff;
    border-radius: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 60px;
    overflow: hidden;
    -webkit-mask-image: url('/assets/images/footer-social.svg');
    mask-image: url('/assets/images/footer-social.svg');
    mask-repeat: no-repeat;
    mask-position: 0 0;
    mask-size: 859px 260px;
}

.pre-footer-card::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 399px;
    height: 260px;
    overflow: visible;
    background: #000;
    pointer-events: none;
    z-index: 1;
    -webkit-mask-image: url('/assets/images/footer-social.svg'), url('/assets/images/footer-pattern.png');
    mask-image: url('/assets/images/footer-social.svg'), url('/assets/images/footer-pattern.png');
    -webkit-mask-repeat: no-repeat, no-repeat;
    mask-repeat: no-repeat, no-repeat;
    -webkit-mask-size: 859px 260px, 600px 600px;
    mask-size: 859px 260px, 600px 600px;
    -webkit-mask-position: -460px 0px, -50px -150px;
    mask-position: -460px 0px, -50px -150px;
    -webkit-mask-composite: intersect;
    mask-composite: intersect;
}

.pre-footer-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    position: relative;
    z-index: 3;
    flex: 1;
}

.pre-footer-right {
    flex: 1;
    position: relative;
    z-index: 2;
    /* 右侧区域给白色条纹装饰 */
}

.pre-footer-text {
    font-family: 'MiSans VF', 'Poppins', 'Noto Sans SC', sans-serif !important;
    font-size: 48px !important;
    font-weight: 900 !important;
    font-stretch: expanded !important;
    line-height: 1.1;
    letter-spacing: -2px;
    color: #000 !important;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    font-display: swap;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-stroke: 0.5px #000;
    -webkit-text-stroke: 0.5px #000;
}

.pre-footer-text p {
    margin: 0 !important;
    font-family: 'MiSans VF', 'Poppins', 'Noto Sans SC', sans-serif !important;
    font-size: 48px !important;
    font-weight: 900 !important;
    font-stretch: expanded !important;
    color: #000 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2) !important;
    text-stroke: 0.5px #000 !important;
    -webkit-text-stroke: 0.5px #000 !important;
    letter-spacing: -2px !important;
    line-height: 1.1 !important;
}

/* 更高优先级的选择器确保样式被应用 */
.pre-footer .pre-footer-card .pre-footer-left .pre-footer-text p {
    font-family: 'MiSans VF', 'Poppins', 'Noto Sans SC', sans-serif !important;
    font-size: 48px !important;
    font-weight: 900 !important;
    font-stretch: expanded !important;
    color: #000 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2) !important;
    text-stroke: 0.5px #000 !important;
    -webkit-text-stroke: 0.5px #000 !important;
}

.back-to-top {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #000;
    color: #fff;
    padding: 10px 20px;
    border-radius: 1100000px; /* 极大的圆角值，类似Figma的rounded-[1.1001e+06px] */
    text-decoration: none;
    font-family: 'MiSans VF', 'Poppins', 'Noto Sans SC', sans-serif;
    font-size: 20px; /* 从16px改为20px，符合Figma设计 */
    font-weight: 500; /* Demibold */
    line-height: 0.88; /* 符合Figma的leading-[0.88] */
    letter-spacing: 0; /* 移除字间距 */
    white-space: nowrap;
    transition: all 0.3s ease;
    min-width: 120px;
    height: 40px;
}

.back-to-top:hover {
    background-color: #333;
    transform: scale(1.05);
}

.back-to-top-icon {
    color: #fff;
    font-size: 16px; /* 图标大小 */
    font-weight: bold;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top span {
    color: #fff;
    font-size: 20px; /* 与按钮整体字体大小保持一致 */
    font-weight: 500;
    line-height: 0.88;
}

/* 主页脚 */
.site-footer {
    background-color: #ffffff;
    padding: 60px 20px;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
}

.footer-logo-wrapper {
    display: none; /* 根据Figma设计，顶部不显示Logo */
}

.footer-logo-img {
    width: 120px;
    height: auto;
    display: block;
}

.footer-links-grid {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 60px;
}

.footer-links-left {
    display: flex;
    gap: 80px;
    justify-content: flex-start;
    align-items: flex-start;
}

.footer-links-right {
    display: flex;
    gap: 80px;
    justify-content: flex-end;
    align-items: flex-start;
}

.footer-column h4 {
    font-size: 20px;
    font-weight: 700; /* Bold */
    color: #6E6E6E; /* 根据Figma设计稿的颜色 */
    margin-bottom: 15px;
    letter-spacing: -2px;
    line-height: 1.07;
}

.footer-column.empty-title h4 {
    visibility: hidden;
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.footer-column li {
    margin: 0;
}

.footer-column a {
    color: #808080; /* 根据Figma设计稿的颜色 */
    text-decoration: none;
    font-size: 20px;
    font-weight: 500; /* Demibold */
    transition: color 0.3s;
    display: block;
    line-height: 1.07;
}

.footer-column a:hover {
    color: #000;
}

.footer-bottom {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
}

.footer-info-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
}

.footer-icp {
    font-weight: 600; /* Semibold */
    font-size: 20px;
    color: #000;
    line-height: 1.07;
}

.footer-copy {
    font-weight: 600; /* Semibold */
    font-size: 20px;
    color: #000;
    line-height: 1.07;
}

.footer-disclaimer {
    font-weight: 700; /* Bold */
    font-size: 16px;
    color: #6E6E6E; /* 根据Figma设计稿的颜色 */
    letter-spacing: -1.6px;
    line-height: 1.07;
    text-align: left;
    width: 100%;
}

/* 返回顶部按钮 */
.back-to-top-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #01d5ff;
    color: white;
    border: none;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(1, 213, 255, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top-btn:hover {
    background: #0099cc;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(1, 213, 255, 0.5);
}

.back-to-top-btn.visible {
    display: flex;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .footer-links-grid {
        gap: 100px;
        flex-wrap: wrap;
    }
    
    .footer-links-left,
    .footer-links-right {
        gap: 60px;
    }
}

@media (max-width: 768px) {
    .pre-footer-card {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 40px 20px;
        height: auto;
    }
    
    .footer-links-grid {
        flex-direction: column;
        gap: 40px;
        align-items: center;
    }
    
    .footer-links-left,
    .footer-links-right {
        flex-direction: column;
        gap: 30px;
        width: 100%;
        align-items: center;
    }
    
    .footer-column {
        text-align: center;
    }
    
    .footer-column h4 {
        font-size: 18px;
    }
    
    .footer-column a {
        font-size: 16px;
    }
    
    .footer-info-row {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .back-to-top {
        padding: 8px 16px;
        font-size: 16px;
    }
    
    .back-to-top-btn {
        width: 45px;
        height: 45px;
        bottom: 20px;
        right: 20px;
    }
} 