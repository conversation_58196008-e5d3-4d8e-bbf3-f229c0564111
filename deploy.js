#!/usr/bin/env node

/**
 * YELOR NETWORK 部署脚本
 * 用于自动化部署和优化
 */

const fs = require('fs');
const path = require('path');

class DeploymentTool {
    constructor() {
        this.config = {
            source: '.',
            output: './dist',
            minify: true,
            optimize: true
        };
    }

    // 创建部署目录
    createDistDirectory() {
        if (!fs.existsSync(this.config.output)) {
            fs.mkdirSync(this.config.output, { recursive: true });
            console.log('✅ 创建部署目录:', this.config.output);
        }
    }

    // 复制文件
    copyFiles() {
        const filesToCopy = [
            'index-new.html',
            'assets/',
            'components/',
            'config/',
            'utils/',
            'README.md'
        ];

        filesToCopy.forEach(file => {
            const sourcePath = path.join(this.config.source, file);
            const destPath = path.join(this.config.output, file);

            if (fs.existsSync(sourcePath)) {
                this.copyRecursive(sourcePath, destPath);
                console.log('📁 复制文件:', file);
            }
        });
    }

    // 递归复制
    copyRecursive(src, dest) {
        const stat = fs.statSync(src);
        
        if (stat.isDirectory()) {
            if (!fs.existsSync(dest)) {
                fs.mkdirSync(dest, { recursive: true });
            }
            
            fs.readdirSync(src).forEach(file => {
                this.copyRecursive(
                    path.join(src, file),
                    path.join(dest, file)
                );
            });
        } else {
            fs.copyFileSync(src, dest);
        }
    }

    // 生成生产版本的HTML
    generateProductionHTML() {
        const htmlPath = path.join(this.config.output, 'index.html');
        const sourceHTML = fs.readFileSync(
            path.join(this.config.source, 'index-new.html'),
            'utf8'
        );

        // 替换开发环境的配置
        let productionHTML = sourceHTML
            .replace(/\/assets\//g, './assets/')
            .replace(/\/components\//g, './components/')
            .replace(/\/config\//g, './config/')
            .replace(/\/utils\//g, './utils/')
            .replace('development', 'production');

        // 添加性能优化标签
        productionHTML = productionHTML.replace(
            '<head>',
            `<head>
    <!-- 性能优化 -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <meta http-equiv="x-dns-prefetch-control" content="on">`
        );

        fs.writeFileSync(htmlPath, productionHTML);
        console.log('📄 生成生产版本 HTML');
    }

    // 优化CSS
    optimizeCSS() {
        const cssDir = path.join(this.config.output, 'assets/css');
        
        if (fs.existsSync(cssDir)) {
            fs.readdirSync(cssDir).forEach(file => {
                if (file.endsWith('.css')) {
                    const filePath = path.join(cssDir, file);
                    let content = fs.readFileSync(filePath, 'utf8');
                    
                    // 简单的CSS优化
                    content = content
                        .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
                        .replace(/\s+/g, ' ') // 压缩空格
                        .replace(/;\s*}/g, '}') // 移除最后的分号
                        .trim();
                    
                    fs.writeFileSync(filePath, content);
                    console.log('🎨 优化CSS:', file);
                }
            });
        }
    }

    // 生成版本信息
    generateVersionInfo() {
        const versionInfo = {
            version: '2.0.0',
            buildTime: new Date().toISOString(),
            environment: 'production',
            features: [
                '企业化重构',
                '防复制保护',
                'API接口预留',
                '响应式设计',
                '性能优化'
            ]
        };

        fs.writeFileSync(
            path.join(this.config.output, 'version.json'),
            JSON.stringify(versionInfo, null, 2)
        );
        console.log('📋 生成版本信息');
    }

    // 生成部署说明
    generateDeploymentGuide() {
        const guide = `# YELOR NETWORK 部署指南

## 部署文件说明

本目录包含了 YELOR NETWORK 的生产版本文件：

- \`index.html\` - 主页面文件
- \`assets/\` - 静态资源目录
- \`components/\` - 组件文件
- \`config/\` - 配置文件
- \`utils/\` - 工具函数
- \`version.json\` - 版本信息

## 服务器要求

- Web 服务器（Apache/Nginx/IIS）
- 支持静态文件服务
- 建议启用 HTTPS
- 建议启用 Gzip 压缩

## 部署步骤

1. 将所有文件上传到 Web 服务器根目录
2. 配置服务器支持 HTML5 History API
3. 设置适当的缓存策略
4. 配置 HTTPS 证书（推荐）

## 缓存策略建议

\`\`\`
# HTML 文件
Cache-Control: no-cache

# CSS/JS 文件
Cache-Control: public, max-age=31536000

# 图片文件
Cache-Control: public, max-age=2592000
\`\`\`

## 安全配置

- 设置适当的 Content-Security-Policy
- 启用 X-Frame-Options
- 配置 X-Content-Type-Options
- 设置 Referrer-Policy

构建时间: ${new Date().toLocaleString()}
版本: 2.0.0
`;

        fs.writeFileSync(
            path.join(this.config.output, 'DEPLOYMENT.md'),
            guide
        );
        console.log('📖 生成部署指南');
    }

    // 执行部署
    async deploy() {
        console.log('🚀 开始部署 YELOR NETWORK...\n');

        try {
            this.createDistDirectory();
            this.copyFiles();
            this.generateProductionHTML();
            
            if (this.config.optimize) {
                this.optimizeCSS();
            }
            
            this.generateVersionInfo();
            this.generateDeploymentGuide();

            console.log('\n✅ 部署完成！');
            console.log(`📁 输出目录: ${this.config.output}`);
            console.log('🌐 可以将 dist 目录上传到您的 Web 服务器');

        } catch (error) {
            console.error('❌ 部署失败:', error.message);
            process.exit(1);
        }
    }
}

// 命令行参数处理
const args = process.argv.slice(2);
const tool = new DeploymentTool();

if (args.includes('--help') || args.includes('-h')) {
    console.log(`
YELOR NETWORK 部署工具

用法:
  node deploy.js [选项]

选项:
  --help, -h     显示帮助信息
  --no-optimize  跳过优化步骤
  --output <dir> 指定输出目录 (默认: ./dist)

示例:
  node deploy.js
  node deploy.js --output ./build
  node deploy.js --no-optimize
`);
    process.exit(0);
}

if (args.includes('--no-optimize')) {
    tool.config.optimize = false;
}

const outputIndex = args.indexOf('--output');
if (outputIndex !== -1 && args[outputIndex + 1]) {
    tool.config.output = args[outputIndex + 1];
}

// 执行部署
tool.deploy(); 