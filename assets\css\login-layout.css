/* 登录页面布局管理 - 基于1080p基准 */
/* 
基准分辨率: 1920×1080
此文件专门管理lottery-logo和lottery-image-section的位置关系
方便在1080p下进行精确调整和计算
*/

/* ========================================
   lottery-logo 基准位置和尺寸
   ======================================== */
.lottery-page .hero-section .lottery-logo {
    position: absolute;
    
    /* 基准位置 - 在1080p下的完美位置 */
    top: 50%;
    left: 30%;
    
    /* 基准尺寸 - 在1080p下的完美尺寸 */
    width: 800px;
    height: 700px;
    
    /* 通用属性 */
    transform: translate(-50%, -50%);
    object-fit: contain;
    z-index: 15;
}

/* ========================================
   lottery-image-section 基准位置
   ======================================== */
.lottery-page .hero-section .lottery-image-section {
    position: absolute;
    
    /* 基准位置 - 在1080p下的完美位置 */
    top: 45%;
    left: 70%;
    
    /* 通用属性 */
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 20;
}

/* ========================================
   lottery-bg-image 基准尺寸
   ======================================== */
.lottery-page .hero-section .lottery-bg-image {
    /* 基准尺寸 - 在1080p下的完美尺寸 */
    max-width: 800px;
    max-height: 600px;
    
    /* 通用属性 */
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* ========================================
   位置关系说明和计算参考
   ======================================== */
/*
在1080p (1920×1080) 基准下的位置关系：

lottery-logo:
- top: 50% = 540px from top
- left: 30% = 576px from left
- 实际顶部位置: 540px - 350px = 190px
- 实际左边位置: 576px - 400px = 176px

lottery-image-section:
- top: 45% = 486px from top  
- left: 70% = 1344px from left
- 实际顶部位置: 486px - (section height/2)
- 实际左边位置: 1344px - (section width/2)

两者的水平距离: 1344px - 576px = 768px
两者的垂直关系: lottery-logo稍微靠下 (540px vs 486px)

调整建议：
- 要让两者顶部对齐，需要调整top值的差异
- 要调整水平间距，修改left值
- 要调整尺寸比例，修改width/height值
*/

/* ========================================
   响应式设计 - 中等屏幕
   ======================================== */
@media (max-width: 1200px) {
    .lottery-page .hero-section .lottery-logo {
        /* 中等屏幕下的调整 */
        width: 640px;  /* 80% of 800px */
        height: 560px; /* 80% of 700px */
        left: 28%;     /* 稍微向左调整 */
    }
    
    .lottery-page .hero-section .lottery-bg-image {
        max-width: 640px; /* 80% of 800px */
        max-height: 480px; /* 80% of 600px */
    }
}

/* ========================================
   响应式设计 - 移动端
   ======================================== */
@media (max-width: 768px) {
    .lottery-page .hero-section .lottery-logo {
        /* 移动端垂直布局 */
        top: 15%;
        left: 50%;
        width: 200px;
        height: 175px;
    }
    
    .lottery-page .hero-section .lottery-image-section {
        /* 移动端登录界面居中 */
        top: 50%;
        left: 50%;
    }
    
    .lottery-page .hero-section .lottery-bg-image {
        max-width: 90vw;
        max-height: 70vh;
        border-radius: 15px;
    }
}

/* ========================================
   调试辅助 - 可选开启
   ======================================== */
/*
.lottery-page .hero-section .lottery-logo {
    border: 2px solid red !important;
    background-color: rgba(255, 0, 0, 0.1) !important;
}

.lottery-page .hero-section .lottery-image-section {
    border: 2px solid blue !important;
    background-color: rgba(0, 0, 255, 0.1) !important;
}

.lottery-page .hero-section .lottery-bg-image {
    border: 2px solid green !important;
}
*/
