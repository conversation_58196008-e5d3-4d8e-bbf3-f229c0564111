/**
 * API 接口配置 - API Configuration
 * 为后端预留的接口配置文件
 */

class APIConfig {
    constructor() {
        this.baseURL = this.getBaseURL();
        this.endpoints = this.initEndpoints();
        this.headers = this.getDefaultHeaders();
    }

    // 获取基础URL
    getBaseURL() {
        const env = process?.env?.NODE_ENV || 'development';
        
        const urls = {
            development: 'http://localhost:3000/api',
            staging: 'https://staging-api.yelor.network/api',
            production: 'https://api.yelor.network/api'
        };

        return urls[env] || urls.development;
    }

    // 初始化API端点
    initEndpoints() {
        return {
            // 用户相关接口
            auth: {
                login: '/auth/login',
                logout: '/auth/logout',
                register: '/auth/register',
                refresh: '/auth/refresh',
                profile: '/auth/profile',
                updateProfile: '/auth/profile/update'
            },

            // 游戏相关接口
            game: {
                servers: '/game/servers',
                playerCount: '/game/players/count',
                serverStatus: '/game/servers/status',
                joinServer: '/game/servers/join',
                leaderboard: '/game/leaderboard',
                playerStats: '/game/players/stats'
            },

            // 新闻相关接口
            news: {
                list: '/news',
                detail: '/news/:id',
                latest: '/news/latest',
                categories: '/news/categories',
                search: '/news/search'
            },

            // BBS论坛相关接口
            forum: {
                categories: '/forum/categories',
                topics: '/forum/topics',
                posts: '/forum/posts',
                createTopic: '/forum/topics/create',
                createPost: '/forum/posts/create',
                userPosts: '/forum/users/:userId/posts'
            },

            // 系统相关接口
            system: {
                status: '/system/status',
                announcements: '/system/announcements',
                maintenance: '/system/maintenance',
                statistics: '/system/statistics'
            },

            // 文件上传接口
            upload: {
                avatar: '/upload/avatar',
                image: '/upload/image',
                file: '/upload/file'
            },

            // 管理员接口
            admin: {
                users: '/admin/users',
                servers: '/admin/servers',
                news: '/admin/news',
                forum: '/admin/forum',
                statistics: '/admin/statistics',
                logs: '/admin/logs'
            }
        };
    }

    // 获取默认请求头
    getDefaultHeaders() {
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };
    }

    // 获取完整的API URL
    getURL(endpoint, params = {}) {
        let url = this.baseURL + endpoint;
        
        // 替换URL参数
        Object.keys(params).forEach(key => {
            url = url.replace(`:${key}`, params[key]);
        });

        return url;
    }

    // 获取认证头
    getAuthHeaders() {
        const token = localStorage.getItem('auth_token');
        return token ? {
            ...this.headers,
            'Authorization': `Bearer ${token}`
        } : this.headers;
    }
}

/**
 * API 请求封装类
 */
class APIClient {
    constructor() {
        this.config = new APIConfig();
        this.requestInterceptors = [];
        this.responseInterceptors = [];
    }

    // 添加请求拦截器
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }

    // 添加响应拦截器
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
    }

    // 执行请求拦截器
    async executeRequestInterceptors(config) {
        let finalConfig = config;
        for (const interceptor of this.requestInterceptors) {
            finalConfig = await interceptor(finalConfig);
        }
        return finalConfig;
    }

    // 执行响应拦截器
    async executeResponseInterceptors(response) {
        let finalResponse = response;
        for (const interceptor of this.responseInterceptors) {
            finalResponse = await interceptor(finalResponse);
        }
        return finalResponse;
    }

    // 基础请求方法
    async request(method, endpoint, data = null, options = {}) {
        try {
            // 准备请求配置
            let requestConfig = {
                method: method.toUpperCase(),
                headers: options.auth !== false ? this.config.getAuthHeaders() : this.config.headers,
                ...options
            };

            // 添加请求体
            if (data && ['POST', 'PUT', 'PATCH'].includes(requestConfig.method)) {
                requestConfig.body = JSON.stringify(data);
            }

            // 执行请求拦截器
            requestConfig = await this.executeRequestInterceptors(requestConfig);

            // 发送请求
            const response = await fetch(this.config.getURL(endpoint, options.params), requestConfig);

            // 执行响应拦截器
            const finalResponse = await this.executeResponseInterceptors(response);

            // 处理响应
            if (!finalResponse.ok) {
                throw new Error(`HTTP ${finalResponse.status}: ${finalResponse.statusText}`);
            }

            const result = await finalResponse.json();
            return result;
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    // GET 请求
    get(endpoint, params = {}, options = {}) {
        return this.request('GET', endpoint, null, { ...options, params });
    }

    // POST 请求
    post(endpoint, data, options = {}) {
        return this.request('POST', endpoint, data, options);
    }

    // PUT 请求
    put(endpoint, data, options = {}) {
        return this.request('PUT', endpoint, data, options);
    }

    // PATCH 请求
    patch(endpoint, data, options = {}) {
        return this.request('PATCH', endpoint, data, options);
    }

    // DELETE 请求
    delete(endpoint, options = {}) {
        return this.request('DELETE', endpoint, null, options);
    }
}

/**
 * 具体业务API封装
 */
class YelorAPI {
    constructor() {
        this.client = new APIClient();
        this.setupInterceptors();
    }

    // 设置拦截器
    setupInterceptors() {
        // 请求拦截器：添加时间戳防止缓存
        this.client.addRequestInterceptor(async (config) => {
            const url = new URL(config.url || window.location.origin);
            url.searchParams.append('_t', Date.now());
            return { ...config, url: url.toString() };
        });

        // 响应拦截器：处理通用错误
        this.client.addResponseInterceptor(async (response) => {
            if (response.status === 401) {
                // 未授权，清除token并跳转登录
                localStorage.removeItem('auth_token');
                window.location.href = '/login';
            }
            return response;
        });
    }

    // 用户认证API
    auth = {
        login: (credentials) => this.client.post(this.client.config.endpoints.auth.login, credentials),
        logout: () => this.client.post(this.client.config.endpoints.auth.logout),
        register: (userData) => this.client.post(this.client.config.endpoints.auth.register, userData),
        refreshToken: () => this.client.post(this.client.config.endpoints.auth.refresh),
        getProfile: () => this.client.get(this.client.config.endpoints.auth.profile),
        updateProfile: (profileData) => this.client.put(this.client.config.endpoints.auth.updateProfile, profileData)
    };

    // 游戏相关API
    game = {
        getServers: () => this.client.get(this.client.config.endpoints.game.servers),
        getPlayerCount: () => this.client.get(this.client.config.endpoints.game.playerCount),
        getServerStatus: (serverId) => this.client.get(this.client.config.endpoints.game.serverStatus, { serverId }),
        joinServer: (serverId) => this.client.post(this.client.config.endpoints.game.joinServer, { serverId }),
        getLeaderboard: (type = 'overall') => this.client.get(this.client.config.endpoints.game.leaderboard, { type }),
        getPlayerStats: (playerId) => this.client.get(this.client.config.endpoints.game.playerStats, { playerId })
    };

    // 新闻相关API
    news = {
        getList: (page = 1, limit = 10) => this.client.get(this.client.config.endpoints.news.list, { page, limit }),
        getDetail: (id) => this.client.get(this.client.config.endpoints.news.detail, { id }),
        getLatest: (count = 5) => this.client.get(this.client.config.endpoints.news.latest, { count }),
        getCategories: () => this.client.get(this.client.config.endpoints.news.categories),
        search: (keyword, page = 1) => this.client.get(this.client.config.endpoints.news.search, { keyword, page })
    };

    // BBS论坛相关API
    forum = {
        getCategories: () => this.client.get(this.client.config.endpoints.forum.categories),
        getTopics: (categoryId, page = 1) => this.client.get(this.client.config.endpoints.forum.topics, { categoryId, page }),
        getPosts: (topicId, page = 1) => this.client.get(this.client.config.endpoints.forum.posts, { topicId, page }),
        createTopic: (topicData) => this.client.post(this.client.config.endpoints.forum.createTopic, topicData),
        createPost: (postData) => this.client.post(this.client.config.endpoints.forum.createPost, postData),
        getUserPosts: (userId, page = 1) => this.client.get(this.client.config.endpoints.forum.userPosts, { userId, page })
    };

    // 系统相关API
    system = {
        getStatus: () => this.client.get(this.client.config.endpoints.system.status),
        getAnnouncements: () => this.client.get(this.client.config.endpoints.system.announcements),
        getMaintenanceStatus: () => this.client.get(this.client.config.endpoints.system.maintenance),
        getStatistics: () => this.client.get(this.client.config.endpoints.system.statistics)
    };
}

// 创建全局API实例
window.YelorAPI = new YelorAPI();

// 导出配置（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APIConfig, APIClient, YelorAPI };
} 