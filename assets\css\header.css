/* 導航欄樣式 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 65px;
    background: rgba(9, 42, 70, 0.03);
    backdrop-filter: blur(15px) saturate(1.1);
    -webkit-backdrop-filter: blur(15px) saturate(1.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 0 2px 12px rgba(0, 50, 120, 0.05);
    z-index: 1000;
    transition: all 0.3s ease;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(179, 50, 50, 0.01);
    pointer-events: none;
}

.header::after {
    display: none;
}

.nav-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 20px;
    z-index: 10;
}

/* Logo區域 */
.logo-section {
    display: flex;
    align-items: center;
    height: 100%;
}

.logo-wrapper {
    display: flex;
    align-items: center;
    padding-left: 13px;
    padding-right: 45px;
}

.logo-icon {
    width: 82px;
    height: 65px;
    background: #ffffff;
    position: relative;
    overflow: hidden;
    margin-right: -38px;
    flex-shrink: 0;
    z-index: 1;
    -webkit-mask-image: url('/assets/images/logo-mask.png');
    mask-image: url('/assets/images/logo-mask.png');
    -webkit-mask-size: 82px 65px;
    mask-size: 82px 65px;
    -webkit-mask-position: 0px 0px;
    mask-position: 0px 0px;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
}

.logo-icon::before {
    display: none;
}

.logo-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-family: 'MiSans VF', 'Poppins', 'Noto Sans SC', sans-serif;
    font-weight: 700;
    font-size: 26px;
    line-height: 22px;
    color: #000000;
    letter-spacing: -1px;
    white-space: nowrap;
    margin-right: -45px;
    flex-shrink: 0;
    position: relative;
    z-index: 2;
}

.logo-line {
    display: block;
}

/* 導航菜單 */
.nav-menu {
    display: flex;
    align-items: center;
    height: 100%;
}

.nav-items {
    display: flex;
    align-items: center;
    gap: 25px;
    padding-right: 36px;
}

.nav-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 65px;
    min-width: 43px;
    padding: 0 8px;
    font-size: 16px;
    font-weight: 700;
    color: #000000;
    text-decoration: none;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-item.active {
    font-weight: 700;
}

.nav-item:hover {
    color: #333333;
    transform: translateY(-2px);
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: #ffffff;
    transition: width 0.3s ease;
}

.nav-item:hover::after,
.nav-item.active::after {
    width: 80%;
}

/* 響應式設計 */
@media (max-width: 1200px) {
    .nav-items {
        gap: 25px;
    }
    
    .nav-item {
        font-size: 16px;
        min-width: 40px;
    }
}

@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        height: auto;
        padding: 10px 20px;
    }
    
    .header {
        height: auto;
        min-height: 77px;
    }
    
    .logo-wrapper {
        padding-left: 10px;
        padding-right: 20px;
        margin-bottom: 10px;
    }
    
    .logo-icon {
        width: 60px;
        height: 50px;
        margin-right: -25px;
        -webkit-mask-size: 60px 50px;
        mask-size: 60px 50px;
    }
    
    .logo-text {
        font-size: 18px;
        line-height: 16px;
        margin-right: -25px;
    }
    
    .nav-items {
        gap: 15px;
        padding-right: 0;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .nav-item {
        font-size: 16px;
        height: 40px;
        min-width: 35px;
    }
}

@media (max-width: 480px) {
    .nav-items {
        gap: 10px;
    }
    
    .nav-item {
        font-size: 14px;
        padding: 0 8px;
    }
    
    .logo-icon {
        width: 45px;
        height: 38px;
        margin-right: -18px;
        -webkit-mask-size: 45px 38px;
        mask-size: 45px 38px;
    }
    
    .logo-text {
        font-size: 16px;
        line-height: 14px;
        margin-right: -18px;
    }
} 