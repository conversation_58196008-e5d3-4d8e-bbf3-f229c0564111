{"name": "yelor-network", "version": "2.0.0", "description": "YELOR NETWORK - 专业的Minecraft服务器网络官网，采用企业化标准重构", "main": "index-new.html", "scripts": {"start": "node start.js", "dev": "node start.js", "build": "node deploy.js", "deploy": "node deploy.js", "serve": "node start.js --port 3000", "help": "node start.js --help", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["minecraft", "server", "gaming", "website", "frontend", "企业化", "防复制", "响应式"], "author": "YELOR NETWORK Team", "license": "Proprietary", "repository": {"type": "git", "url": "https://github.com/yelor-network/website.git"}, "bugs": {"url": "https://github.com/yelor-network/website/issues"}, "homepage": "https://yelor.network", "engines": {"node": ">=14.0.0"}, "devDependencies": {}, "dependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "config": {"port": 8000, "host": "localhost"}, "directories": {"lib": "./assets/js", "doc": "./docs"}, "files": ["index-new.html", "assets/", "components/", "config/", "utils/", "README.md", "start.js", "deploy.js"], "metadata": {"features": ["企业化重构", "防复制保护", "API接口预留", "响应式设计", "性能优化", "模块化架构", "SEO优化", "无障碍访问"], "technologies": ["HTML5", "CSS3", "JavaScript ES6+", "Node.js", "Responsive Design", "Progressive Enhancement"], "browsers": {"chrome": ">=80", "firefox": ">=75", "safari": ">=13", "edge": ">=80"}, "performance": {"lighthouse": {"performance": ">90", "accessibility": ">95", "best-practices": ">90", "seo": ">95"}}}}