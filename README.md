# YELOR NETWORK - 企业化重构版

[![Node.js Version](https://img.shields.io/badge/node-%3E%3D14.0.0-brightgreen)](https://nodejs.org/)
[![License](https://img.shields.io/badge/License-Proprietary-blue.svg)](https://yelor.network)

## 项目概述

YELOR NETWORK 是一个专业的 Minecraft 服务器网络官网，采用企业化标准重构，具有以下特点：

- 🚀 **模块化架构** - 组件化开发，易于维护和扩展
- 🔒 **防复制保护** - 多层防护机制，保护网站内容
- 🌐 **API 预留** - 完整的后端接口预留，支持数据驱动
- 📱 **响应式设计** - 适配各种设备和屏幕尺寸
- ⚡ **性能优化** - 代码分割、懒加载、资源优化

## 开发指南

### 常用命令
```bash
# 开发模式 (端口 8000)
npm run start

# 生产构建
npm run build

# 自定义端口启动
npm run serve -- --port 3000
```

### 技术栈
- 前端架构: 模块化HTML/CSS
- 样式方案: 原生CSS特性
- 构建工具: Node.js 脚本
- 代码规范: 企业级代码规范

## 项目结构

```
yelorweb/
├── assets/                 # 静态资源目录
│   ├── css/               # 样式文件
│   │   ├── base.css       # 基础样式和重置
│   │   ├── header.css     # 头部导航样式
│   │   ├── hero.css       # 英雄区域样式
│   │   ├── game-modes.css # 游戏模式样式
│   │   ├── news.css       # 新闻区域样式
│   │   ├── bbs.css        # 论坛区域样式
│   │   └── footer.css     # 页脚样式
│   └── js/                # JavaScript 文件
│       ├── protection.js  # 防复制保护系统
│       └── main.js        # 主应用程序
├── components/            # 组件目录
│   └── header.html       # 头部组件
├── config/               # 配置文件
│   └── api.js           # API 接口配置
├── utils/               # 工具函数
│   └── helpers.js       # 辅助函数库
├── api/                 # API 接口目录（预留）
├── index-new.html       # 新版主页面
├── index.html          # 原版页面（保留）
├── styles.css          # 原版样式（保留）
├── script.js           # 原版脚本（保留）
└── README.md           # 项目说明

## 贡献指南

欢迎通过 GitHub Issues 提交问题或 Pull Request：
- 遵循现有代码风格
- 提交前运行基础测试
- 更新相关文档

## 许可证

本项目采用 [Proprietary License](https://yelor.network/license)，详情请联系 YELOR NETWORK 团队。

---
![YELOR Network Architecture](assets/images/variety.png)
```

## 快速入门

### 安装要求
- Node.js 14.0 或更高版本
- Git 2.20 或更高版本

### 安装步骤
```bash
# 克隆仓库
git clone https://github.com/yelor-network/website.git

# 进入项目目录
cd yelorweb

# 启动开发服务器
npm run start
```

## 核心功能

### 1. 防复制保护系统

- **右键禁用** - 防止右键菜单访问
- **快捷键拦截** - 禁用 F12、Ctrl+U、Ctrl+S 等开发者工具快捷键
- **文本选择禁用** - 防止文本复制
- **图片拖拽禁用** - 防止图片保存
- **开发者工具检测** - 实时检测并阻止开发者工具打开
- **控制台禁用** - 重写 console 方法
- **水印保护** - 动态添加版权水印

### 2. 模块化架构

#### 组件系统
- **Header 组件** - 响应式导航栏，支持用户登录状态
- **Footer 组件** - 页脚信息和友情链接
- **动态加载** - 组件按需加载，提高性能

#### 样式分离
- **基础样式** - 重置样式、字体、全局变量
- **组件样式** - 每个组件独立的样式文件
- **响应式设计** - 移动端适配

### 3. API 接口预留

#### 用户系统
```javascript
// 用户登录
await YelorAPI.auth.login({ username, password });

// 获取用户信息
await YelorAPI.auth.getProfile();

// 更新用户资料
await YelorAPI.auth.updateProfile(profileData);
```

#### 游戏数据
```javascript
// 获取服务器状态
await YelorAPI.game.getServerStatus(serverId);

// 获取玩家数量
await YelorAPI.game.getPlayerCount();

// 获取排行榜
await YelorAPI.game.getLeaderboard(type);
```

#### 新闻系统
```javascript
// 获取新闻列表
await YelorAPI.news.getList(page, limit);

// 获取最新新闻
await YelorAPI.news.getLatest(count);

// 搜索新闻
await YelorAPI.news.search(keyword, page);
```

### 4. 工具函数库

#### DOM 操作
```javascript
// 元素选择
YelorUtils.DOM.$('#element-id');
YelorUtils.DOM.$$('.class-name');

// 动态加载 HTML
await YelorUtils.DOM.loadHTML('/path/to/component.html', container);

// 事件绑定
YelorUtils.DOM.on(element, 'click', handler);
```

#### 动画效果
```javascript
// 平滑滚动
YelorUtils.Animation.scrollTo(target, offset, duration);

// 淡入淡出
YelorUtils.Animation.fadeIn(element);
YelorUtils.Animation.fadeOut(element);
```

#### 通知系统
```javascript
// 显示通知
YelorUtils.Notification.success('操作成功');
YelorUtils.Notification.error('操作失败');
YelorUtils.Notification.warning('警告信息');
YelorUtils.Notification.info('提示信息');
```

## 使用方法

### 1. 本地开发

```bash
# 启动本地服务器
python -m http.server 8000

# 访问新版页面
http://localhost:8000/index-new.html

# 访问原版页面（对比）
http://localhost:8000/index.html
```

### 2. 部署说明

#### 静态部署
- 将所有文件上传到 Web 服务器
- 确保服务器支持 HTML5 History API
- 配置 HTTPS（推荐）

#### CDN 优化
- 将 `assets/` 目录上传到 CDN
- 更新 HTML 中的资源链接
- 启用 Gzip 压缩

### 3. 后端集成

#### API 服务器配置
1. 修改 `config/api.js` 中的 `baseURL`
2. 实现对应的后端接口
3. 配置 CORS 策略

#### 数据库设计
参考 API 接口设计数据库表结构：
- `users` - 用户表
- `servers` - 服务器表
- `news` - 新闻表
- `forum_categories` - 论坛分类表
- `forum_topics` - 论坛主题表
- `forum_posts` - 论坛帖子表

## 技术特性

### 1. 性能优化

- **代码分割** - CSS 和 JS 按功能分离
- **懒加载** - 组件按需加载
- **缓存策略** - 静态资源缓存
- **压缩优化** - 代码压缩和 Gzip

### 2. 安全防护

- **XSS 防护** - 输入过滤和输出转义
- **CSRF 防护** - Token 验证
- **内容保护** - 多层防复制机制
- **访问控制** - 基于角色的权限系统

### 3. 用户体验

- **响应式设计** - 移动端友好
- **动画效果** - 平滑过渡动画
- **交互反馈** - 加载状态和错误提示
- **无障碍访问** - ARIA 标签和语义化 HTML

## 浏览器兼容性

- **现代浏览器** - Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **移动端** - iOS Safari 13+, Chrome Mobile 80+
- **不支持** - IE 11 及以下版本

## 开发规范

### 1. 代码风格

- **JavaScript** - ES6+ 语法，使用 const/let
- **CSS** - BEM 命名规范，移动端优先
- **HTML** - 语义化标签，ARIA 无障碍

### 2. 文件命名

- **组件文件** - kebab-case（如：header-component.html）
- **样式文件** - 功能命名（如：base.css, header.css）
- **脚本文件** - camelCase（如：mainApp.js）

### 3. 注释规范

- **函数注释** - JSDoc 格式
- **CSS 注释** - 分块注释，说明用途
- **HTML 注释** - 组件边界标记

## 更新日志

### v2.0.0 (2025-01-15)
- 🎉 **企业化重构** - 完全重写代码架构
- 🔒 **防复制系统** - 新增多层防护机制
- 🌐 **API 预留** - 完整的后端接口设计
- 📱 **响应式优化** - 改进移动端体验
- ⚡ **性能提升** - 代码分割和懒加载

### v1.0.0 (2025-01-01)
- 🚀 **初始版本** - 基础功能实现
- 🎨 **UI 设计** - 完成视觉设计
- 📝 **内容管理** - 新闻和公告系统

## 许可证

Copyright © 2025 YELOR NETWORK. All rights reserved.

---

**注意：** 本项目包含防复制保护系统，请遵守相关法律法规和使用条款。