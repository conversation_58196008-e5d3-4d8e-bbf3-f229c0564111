/* 未登录弹窗样式 - lottery-no-login.css */
/* 
弹窗背景图片: /assets/images/lottery-no-login-css.png
功能: 用户未登录时点击"立刻申请"按钮显示的弹窗
*/

/* ========================================
   弹窗容器 - 全屏覆盖
   ======================================== */
.lottery-no-login-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10000; /* 最高层级 */
    display: none; /* 默认隐藏 */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                visibility 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none;
    /* GPU加速 */
    will-change: opacity, visibility;
}

/* 弹窗显示状态 */
.lottery-no-login-modal.show {
    display: flex;
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* ========================================
   背景虚化层 - 除header外的所有内容
   ======================================== */
.lottery-no-login-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* 初始状态 - 无模糊效果 */
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
    background: rgba(0, 0, 0, 0);
    cursor: pointer; /* 点击背景关闭弹窗 */

    /* 渐进动画效果 */
    transition: backdrop-filter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                -webkit-backdrop-filter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                background 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* GPU加速优化 */
    will-change: backdrop-filter, background;
    transform: translateZ(0);
}

/* 弹窗显示时的毛玻璃效果 - 渐进出现 */
.lottery-no-login-modal.show .lottery-no-login-backdrop {
    backdrop-filter: blur(15px) saturate(120%);
    -webkit-backdrop-filter: blur(15px) saturate(120%);
    background: rgba(0, 0, 0, 0.4);
}

/* Header区域不虚化 - 通过遮罩实现 */
.lottery-no-login-backdrop::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px; /* Header高度，根据实际调整 */
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: transparent;
    z-index: 1;
}

/* ========================================
   弹窗内容容器 - 居中显示
   ======================================== */
.lottery-no-login-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    pointer-events: none; /* 允许点击穿透到背景 */
}

/* ========================================
   弹窗图片 - 背景图片居中显示
   ======================================== */
.lottery-no-login-image {
    background-image: url('/assets/images/lottery-no-login-css.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain; /* 保持原始尺寸比例 */

    /* 图片容器尺寸 - 根据实际图片调整 */
    width: 800px;
    height: 600px;
    max-width: 90vw;
    max-height: 80vh;

    /* 动画效果 */
    transform: scale(0.8) translateZ(0);
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: auto; /* 图片本身可以接收事件，但不关闭弹窗 */
    /* GPU加速 */
    will-change: transform;
    backface-visibility: hidden;

    /* 确保图片在容器中居中 */
    position: relative;

    /* 为按钮提供定位上下文 */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ========================================
   弹窗内的登录按钮 - 与立刻申请按钮样式完全相同
   ======================================== */
.lottery-no-login-button {
    /* 基础样式 - 与立刻申请按钮完全相同 */
    width: 150px !important;
    height: 40px !important;
    border-radius: 40px !important;
    background-color: #000000 !important;
    color: #ffffff !important;
    border: none !important;
    outline: none !important;
    cursor: pointer !important;

    /* 位置控制 */
    position: absolute !important;
    bottom: 119px !important; /* 距离图片底部的距离 */
    left: 31% !important;
    transform: translateX(-50%) !important;

    /* 文字样式 */
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 16px !important;
    font-weight: 500 !important;

    /* 交互效果 */
    transition: all 0.3s ease !important;
    user-select: none !important;
    overflow: hidden !important;

    /* 居中对齐 */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    /* 确保按钮在图片上方 */
    z-index: 10 !important;
}

/* 登录按钮悬停效果 - 与立刻申请按钮完全相同 */
.lottery-no-login-button:hover {
    background: #01C8FF !important;
    color: #ffffff !important;
}

/* 登录按钮点击效果 */
.lottery-no-login-button:active {
    transform: translateX(-50%) scale(0.98) !important;
}

/* 登录按钮文字 */
.lottery-no-login-button-text {
    pointer-events: none;
    position: relative;
    z-index: 2;
}

/* 弹窗显示时的图片动画 */
.lottery-no-login-modal.show .lottery-no-login-image {
    transform: scale(1) translateZ(0);
}

/* ========================================
   响应式设计
   ======================================== */
/* 中等屏幕适配 */
@media (max-width: 1200px) {
    .lottery-no-login-image {
        width: 500px;
        height: 350px;
    }

    .lottery-no-login-backdrop::before {
        height: 70px; /* 调整header高度 */
    }

    .lottery-no-login-button {
        width: 130px !important;
        height: 36px !important;
        font-size: 14px !important;
        bottom: 60px !important;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .lottery-no-login-image {
        width: 400px;
        height: 280px;
        max-width: 85vw;
        max-height: 70vh;
    }

    .lottery-no-login-backdrop {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }

    .lottery-no-login-backdrop::before {
        height: 60px; /* 移动端header高度 */
    }

    .lottery-no-login-button {
        width: 120px !important;
        height: 32px !important;
        font-size: 13px !important;
        bottom: 40px !important;
    }
}

/* 小屏幕适配 */
@media (max-width: 480px) {
    .lottery-no-login-image {
        width: 320px;
        height: 220px;
        max-width: 80vw;
        max-height: 60vh;
    }

    .lottery-no-login-button {
        width: 100px !important;
        height: 28px !important;
        font-size: 12px !important;
        bottom: 30px !important;
    }
}

/* ========================================
   动画关键帧
   ======================================== */
/* 弹窗打开动画 */
@keyframes modal-fade-in {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 弹窗关闭动画 */
@keyframes modal-fade-out {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

/* ========================================
   特殊效果增强
   ======================================== */
/* 弹窗图片悬停效果（可选） */
.lottery-no-login-image:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

/* 背景虚化增强效果 */
.lottery-no-login-backdrop {
    background: radial-gradient(
        circle at center,
        rgba(0, 0, 0, 0.2) 0%,
        rgba(0, 0, 0, 0.4) 100%
    );
}

/* ========================================
   浏览器兼容性
   ======================================== */
/* Safari兼容性 */
@supports (-webkit-backdrop-filter: blur(10px)) {
    .lottery-no-login-backdrop {
        -webkit-backdrop-filter: blur(10px);
    }
}

/* Firefox兼容性 */
@supports (backdrop-filter: blur(10px)) {
    .lottery-no-login-backdrop {
        backdrop-filter: blur(10px);
    }
}

/* 不支持backdrop-filter的浏览器降级方案 */
@supports not (backdrop-filter: blur(10px)) and not (-webkit-backdrop-filter: blur(10px)) {
    .lottery-no-login-backdrop {
        background: rgba(0, 0, 0, 0.6);
    }
}

/* ========================================
   调试模式（开发时可启用）
   ======================================== */
/*
.lottery-no-login-modal {
    border: 2px solid red;
}

.lottery-no-login-backdrop {
    border: 2px solid blue;
}

.lottery-no-login-image {
    border: 2px solid green;
}
*/
