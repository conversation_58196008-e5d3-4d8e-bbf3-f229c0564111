/* 紧凑布局调整 - Compact Layout Adjustments */

/* 确保所有主要区域之间没有额外间距 */
.hero-section,
.game-modes-section,
.news-section,
.bbs-section,
.pre-footer,
.site-footer {
    margin-top: 0;
    margin-bottom: 0;
}

/* 游戏模式区域调整 */
.game-modes-section {
    padding: 0;
    margin: 0;
}

/* 新闻区域调整 */
.news-section {
    margin: 0;
    padding: 0;
}

/* BBS区域调整 */
.bbs-section {
    margin: 0;
    padding: 0;
}

/* Pre-footer区域调整 */
.pre-footer {
    padding: 40px 20px; /* 从 60px 减少到 40px */
    margin: 0;
}

/* Footer区域调整 */
.site-footer {
    padding: 40px 20px; /* 从 60px 减少到 40px */
    margin: 0;
}

/* 确保main-content没有额外的margin */
.main-content {
    margin-bottom: 0;
}

/* 垂直对齐修正 - 确保各区域的分割线对齐 */
.hero-section,
.game-modes-section,
.news-section {
    /* 确保这些区域的容器对齐到相同的网格 */
    box-sizing: border-box;
}

/* 确保game-modes区域的分割线与hero区域元素对齐 */
.game-modes-container {
    position: relative;
    box-sizing: border-box;
}

/* 确保news区域的分割线与上面区域对齐 */
.news-section {
    position: relative;
    box-sizing: border-box;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .pre-footer {
        padding: 30px 20px;
    }
    
    .site-footer {
        padding: 30px 20px;
    }
}

@media (max-width: 480px) {
    .pre-footer {
        padding: 20px 15px;
    }
    
    .site-footer {
        padding: 20px 15px;
    }
} 