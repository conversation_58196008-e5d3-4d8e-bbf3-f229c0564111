#!/usr/bin/env node

/**
 * YELOR NETWORK 本地开发服务器
 * 用于本地开发和测试
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

class DevServer {
    constructor(port = 8000) {
        this.port = port;
        this.mimeTypes = {
            '.html': 'text/html',
            '.css': 'text/css',
            '.js': 'application/javascript',
            '.json': 'application/json',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml',
            '.ico': 'image/x-icon',
            '.woff': 'font/woff',
            '.woff2': 'font/woff2',
            '.ttf': 'font/ttf',
            '.eot': 'application/vnd.ms-fontobject'
        };
    }

    getMimeType(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        return this.mimeTypes[ext] || 'application/octet-stream';
    }

    serveFile(filePath, res) {
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end(`
                    <h1>404 - 文件未找到</h1>
                    <p>请求的文件 <code>${filePath}</code> 不存在。</p>
                    <p><a href="/">返回首页</a></p>
                `);
                return;
            }

            const mimeType = this.getMimeType(filePath);
            res.writeHead(200, { 
                'Content-Type': mimeType,
                'Cache-Control': 'no-cache'
            });
            res.end(data);
        });
    }

    handleRequest(req, res) {
        const parsedUrl = url.parse(req.url, true);
        let pathname = parsedUrl.pathname;

        // 添加CORS头
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        // 处理OPTIONS请求
        if (req.method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }

        // 路由处理
        if (pathname === '/') {
            pathname = '/index-new.html';
        } else if (pathname === '/old') {
            pathname = '/index.html';
        }

        // 构建文件路径
        let filePath = path.join(__dirname, pathname);

        // 检查文件是否存在
        fs.stat(filePath, (err, stats) => {
            if (err || !stats.isFile()) {
                // 如果是目录，尝试查找index.html
                if (!err && stats.isDirectory()) {
                    filePath = path.join(filePath, 'index.html');
                    fs.stat(filePath, (err, stats) => {
                        if (err || !stats.isFile()) {
                            this.serve404(res, pathname);
                        } else {
                            this.serveFile(filePath, res);
                        }
                    });
                } else {
                    this.serve404(res, pathname);
                }
            } else {
                this.serveFile(filePath, res);
            }
        });

        // 记录请求
        console.log(`${new Date().toISOString()} - ${req.method} ${pathname}`);
    }

    serve404(res, pathname) {
        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>404 - YELOR NETWORK</title>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        margin: 0;
                        padding: 0;
                        min-height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                    }
                    .container {
                        max-width: 600px;
                        padding: 40px 20px;
                    }
                    h1 {
                        font-size: 72px;
                        margin: 0 0 20px 0;
                        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
                    }
                    h2 {
                        font-size: 24px;
                        margin: 0 0 20px 0;
                        opacity: 0.9;
                    }
                    p {
                        font-size: 16px;
                        margin: 0 0 30px 0;
                        opacity: 0.8;
                    }
                    .links {
                        display: flex;
                        gap: 20px;
                        justify-content: center;
                        flex-wrap: wrap;
                    }
                    a {
                        display: inline-block;
                        padding: 12px 24px;
                        background: rgba(255,255,255,0.2);
                        color: white;
                        text-decoration: none;
                        border-radius: 25px;
                        transition: all 0.3s ease;
                        backdrop-filter: blur(10px);
                    }
                    a:hover {
                        background: rgba(255,255,255,0.3);
                        transform: translateY(-2px);
                    }
                    code {
                        background: rgba(0,0,0,0.2);
                        padding: 2px 6px;
                        border-radius: 4px;
                        font-family: 'Courier New', monospace;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>404</h1>
                    <h2>页面未找到</h2>
                    <p>请求的页面 <code>${pathname}</code> 不存在。</p>
                    <div class="links">
                        <a href="/">新版首页</a>
                        <a href="/old">原版首页</a>
                        <a href="/README.md">项目说明</a>
                    </div>
                </div>
            </body>
            </html>
        `);
    }

    start() {
        const server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });

        server.listen(this.port, () => {
            console.log(`
🚀 YELOR NETWORK 开发服务器已启动

📍 本地地址:
   - 新版页面: http://localhost:${this.port}/
   - 原版页面: http://localhost:${this.port}/old
   - 项目说明: http://localhost:${this.port}/README.md

📁 服务目录: ${__dirname}
⏰ 启动时间: ${new Date().toLocaleString()}

💡 提示:
   - 按 Ctrl+C 停止服务器
   - 修改文件后刷新浏览器即可看到变化
   - 所有请求都会在控制台显示

🔧 开发工具:
   - 部署: node deploy.js
   - 帮助: node start.js --help
            `);
        });

        // 优雅关闭
        process.on('SIGINT', () => {
            console.log('\n\n👋 正在关闭服务器...');
            server.close(() => {
                console.log('✅ 服务器已关闭');
                process.exit(0);
            });
        });

        // 错误处理
        server.on('error', (err) => {
            if (err.code === 'EADDRINUSE') {
                console.error(`❌ 端口 ${this.port} 已被占用，请尝试其他端口:`);
                console.error(`   node start.js --port ${this.port + 1}`);
            } else {
                console.error('❌ 服务器错误:', err.message);
            }
            process.exit(1);
        });
    }
}

// 命令行参数处理
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    console.log(`
YELOR NETWORK 开发服务器

用法:
  node start.js [选项]

选项:
  --help, -h        显示帮助信息
  --port <number>   指定端口号 (默认: 8000)

示例:
  node start.js
  node start.js --port 3000

路由:
  /                 新版首页 (index-new.html)
  /old              原版首页 (index.html)
  /README.md        项目说明
  /assets/          静态资源
  /components/      组件文件
  /config/          配置文件
  /utils/           工具函数
`);
    process.exit(0);
}

let port = 8000;
const portIndex = args.indexOf('--port');
if (portIndex !== -1 && args[portIndex + 1]) {
    const customPort = parseInt(args[portIndex + 1]);
    if (!isNaN(customPort) && customPort > 0 && customPort < 65536) {
        port = customPort;
    } else {
        console.error('❌ 无效的端口号，请使用 1-65535 之间的数字');
        process.exit(1);
    }
}

// 启动服务器
const server = new DevServer(port);
server.start(); 