/* 抽奖成功弹窗样式 - lottery-receive.css */
/* 
弹窗背景图片: /assets/images/lottery-receive-bg.png
功能: 用户抽奖成功时显示的弹窗
*/

/* 预加载背景图片，避免首次显示时的加载延迟 */
.lottery-receive-preload {
    position: absolute;
    top: -9999px;
    left: -9999px;
    width: 1px;
    height: 1px;
    background-image: url('/assets/images/lottery-receive-bg.png');
    opacity: 0;
    pointer-events: none;
}

/* ========================================
   弹窗容器 - 全屏覆盖
   ======================================== */
.lottery-receive-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10000; /* 最高层级 */
    display: none; /* 默认隐藏 */
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                visibility 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none;
    /* GPU加速 */
    will-change: opacity, visibility;
}

/* 弹窗显示状态 */
.lottery-receive-modal.show {
    display: flex;
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* ========================================
   背景虚化层 - 除header外的所有内容
   ======================================== */
.lottery-receive-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* 初始状态 - 无模糊效果 */
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
    background: rgba(0, 0, 0, 0);
    cursor: pointer; /* 点击背景关闭弹窗 */
    
    /* 渐进动画效果 */
    transition: backdrop-filter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                -webkit-backdrop-filter 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                background 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    
    /* GPU加速优化 */
    will-change: backdrop-filter, background;
    transform: translateZ(0);
}

/* 弹窗显示时的毛玻璃效果 - 渐进出现 */
.lottery-receive-modal.show .lottery-receive-backdrop {
    backdrop-filter: blur(15px) saturate(120%);
    -webkit-backdrop-filter: blur(15px) saturate(120%);
    background: rgba(0, 0, 0, 0.4);
}

/* Header区域保护层 - 避免虚化Header */
.lottery-receive-backdrop::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px; /* Header高度，根据实际调整 */
    background: transparent;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    z-index: 10001; /* 比弹窗更高 */
    pointer-events: none;
}

/* ========================================
   弹窗内容容器 - 居中显示
   ======================================== */
.lottery-receive-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    pointer-events: none; /* 允许点击穿透到背景 */
}

/* ========================================
   弹窗图片 - 背景图片居中显示
   ======================================== */
.lottery-receive-image {
    background-image: url('/assets/images/lottery-receive-bg.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain; /* 保持原始尺寸比例 */
    
    /* 图片容器尺寸 - 根据实际图片调整 */
    width: 800px;
    height: 600px;
    max-width: 90vw;
    max-height: 80vh;
    
    /* 动画效果 */
    transform: scale(0.8) translateZ(0);
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: auto; /* 图片本身可以接收事件，但不关闭弹窗 */
    /* GPU加速 */
    will-change: transform;
    backface-visibility: hidden;
    
    /* 确保图片在容器中居中 */
    position: relative;
    
    /* 为按钮提供定位上下文 */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 弹窗显示时的图片动画 */
.lottery-receive-modal.show .lottery-receive-image {
    transform: scale(1) translateZ(0);
}

/* ========================================
   弹窗内的确认收取按钮 - 与立刻申请按钮样式完全相同
   ======================================== */
.lottery-receive-button {
    /* 基础样式 - 与立刻申请按钮完全相同 */
    width: 150px !important;
    height: 40px !important;
    border-radius: 40px !important;
    background-color: #000000 !important;
    color: #ffffff !important;
    border: none !important;
    outline: none !important;
    cursor: pointer !important;
    
    /* 位置控制 */
    position: absolute !important;
    bottom: 119px !important; /* 距离图片底部的距离 */
    left: 31% !important;
    transform: translateX(-50%) !important;
    
    /* 文字样式 */
    font-family: 'MiSans VF', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    
    /* 交互效果 */
    transition: all 0.3s ease !important;
    user-select: none !important;
    overflow: hidden !important;
    
    /* 居中对齐 */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    
    /* 确保按钮在图片上方 */
    z-index: 10 !important;
}

/* 确认收取按钮悬停效果 - 与立刻申请按钮完全相同 */
.lottery-receive-button:hover {
    background: #01C8FF !important;
    color: #ffffff !important;
}

/* 确认收取按钮点击效果 */
.lottery-receive-button:active {
    transform: translateX(-50%) scale(0.98) !important;
}

/* 确认收取按钮文字 */
.lottery-receive-button-text {
    pointer-events: none;
    position: relative;
    z-index: 2;
}

/* ========================================
   响应式设计
   ======================================== */
/* 中等屏幕适配 */
@media (max-width: 1200px) {
    .lottery-receive-image {
        width: 500px;
        height: 350px;
    }
    
    .lottery-receive-backdrop::before {
        height: 70px; /* 调整header高度 */
    }
    
    .lottery-receive-button {
        width: 130px !important;
        height: 36px !important;
        font-size: 14px !important;
        bottom: 60px !important;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .lottery-receive-image {
        width: 400px;
        height: 280px;
        max-width: 85vw;
        max-height: 70vh;
    }
    
    .lottery-receive-backdrop {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
    
    .lottery-receive-backdrop::before {
        height: 60px; /* 移动端header高度 */
    }
    
    .lottery-receive-button {
        width: 120px !important;
        height: 32px !important;
        font-size: 13px !important;
        bottom: 40px !important;
    }
}

/* 小屏幕适配 */
@media (max-width: 480px) {
    .lottery-receive-image {
        width: 320px;
        height: 220px;
        max-width: 80vw;
        max-height: 60vh;
    }
    
    .lottery-receive-button {
        width: 100px !important;
        height: 28px !important;
        font-size: 12px !important;
        bottom: 30px !important;
    }
}

/* ========================================
   浏览器兼容性
   ======================================== */
/* Safari兼容性 */
@supports (-webkit-backdrop-filter: blur(15px)) {
    .lottery-receive-backdrop {
        -webkit-backdrop-filter: blur(15px);
    }
}

/* Firefox兼容性 */
@supports (backdrop-filter: blur(15px)) {
    .lottery-receive-backdrop {
        backdrop-filter: blur(15px);
    }
}

/* 不支持backdrop-filter的浏览器降级方案 */
@supports not (backdrop-filter: blur(15px)) and not (-webkit-backdrop-filter: blur(15px)) {
    .lottery-receive-backdrop {
        background: rgba(0, 0, 0, 0.6);
    }
}
