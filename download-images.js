#!/usr/bin/env node

/**
 * YELOR NETWORK 图片下载工具
 * 自动下载所有远程图片到本地
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

class ImageDownloader {
    constructor() {
        this.baseUrl = 'http://localhost:3845/assets/';
        this.localDir = './assets/images/';
        this.imageMap = new Map();
        
        // 确保本地目录存在
        if (!fs.existsSync(this.localDir)) {
            fs.mkdirSync(this.localDir, { recursive: true });
        }
    }

    // 提取的图片文件名列表
    getImageList() {
        return [
            '458b721555f9ee7387c43edb5f63901054f00243.png', // logo-mask.png
            '1371fdf97dc7747478b8269a880ca646bf6ac147.png', // hero-bg.png
            '480cbd840839b51b75aaa929759f3adf1b3594af.png', // hero-logo.png
            '79389d5d9b341bc7ed4cec8e4175ccbc4f137120.png', // news-cover.png
            '7e8e662ee92ac19fc3df7abb30c0a4d0fc11f481.svg', // news-arrow.svg
            '44bb2edafb67bcd87a56e3d18e1ceee25de23860.svg', // news-decoration.svg
            '0b075fe5e9a7a05c7f936678dea04bd904383d12.svg', // news-tag-arrow.svg
            '7f4d74422562d3db38d03f99bdb3c3b1aae34b96.png', // news-1.png
            'b28a71269de1162bb10445e5ea495fc7413303db.png', // news-2.png
            '0c26a55795f9393716d4df3d463246a3097d8638.png', // news-3.png
            '840324aa837129a5a61aefcb158b1b28d4feb255.svg', // footer-social.svg
            'c5efeab3eb2bca2f969ad9142de58b951d043ca9.png', // footer-pattern.png
            '67d6af5fa88968b765127de688f4552479ab81a2.svg', // decoration.svg
            'c23fd1ffe150434fde3c8b5df0143facdef75edd.svg', // arrow.svg
            '24735d2672f13a43bddfd39c3abd762c614ed290.svg', // bbs-icon-1.svg
            '341dfff9ea1f9377ad3c60cccc7592642086312a.svg', // bbs-icon-2.svg
            'a78d31c166214ed83b9ab76a732dbbc7c6133a3e.svg', // bbs-icon-3.svg
            'd0ff3246010c45163e55aa03e2b4b84360530a06.png', // logo.png
            'cfae8055eda8f541583a6849d6fa5a2a40234587.svg', // bbs-icon-4.svg
            '129f63186fb6b733447f9e653575d6f73b95a280.svg', // bbs-icon-5.svg
            'f93c95a76591fe12c8973068e33bbb55628e387f.svg', // bbs-title-icon.svg
        ];
    }

    // 获取文件的友好名称
    getFriendlyName(filename) {
        const mapping = {
            '458b721555f9ee7387c43edb5f63901054f00243.png': 'logo-mask.png',
            '1371fdf97dc7747478b8269a880ca646bf6ac147.png': 'hero-bg.png',
            '480cbd840839b51b75aaa929759f3adf1b3594af.png': 'hero-logo.png',
            '79389d5d9b341bc7ed4cec8e4175ccbc4f137120.png': 'news-cover.png',
            '7e8e662ee92ac19fc3df7abb30c0a4d0fc11f481.svg': 'news-arrow.svg',
            '44bb2edafb67bcd87a56e3d18e1ceee25de23860.svg': 'news-decoration.svg',
            '0b075fe5e9a7a05c7f936678dea04bd904383d12.svg': 'news-tag-arrow.svg',
            '7f4d74422562d3db38d03f99bdb3c3b1aae34b96.png': 'news-1.png',
            'b28a71269de1162bb10445e5ea495fc7413303db.png': 'news-2.png',
            '0c26a55795f9393716d4df3d463246a3097d8638.png': 'news-3.png',
            '840324aa837129a5a61aefcb158b1b28d4feb255.svg': 'footer-social.svg',
            'c5efeab3eb2bca2f969ad9142de58b951d043ca9.png': 'footer-pattern.png',
            '67d6af5fa88968b765127de688f4552479ab81a2.svg': 'decoration.svg',
            'c23fd1ffe150434fde3c8b5df0143facdef75edd.svg': 'arrow.svg',
            '24735d2672f13a43bddfd39c3abd762c614ed290.svg': 'bbs-icon-1.svg',
            '341dfff9ea1f9377ad3c60cccc7592642086312a.svg': 'bbs-icon-2.svg',
            'a78d31c166214ed83b9ab76a732dbbc7c6133a3e.svg': 'bbs-icon-3.svg',
            'd0ff3246010c45163e55aa03e2b4b84360530a06.png': 'logo.png',
            'cfae8055eda8f541583a6849d6fa5a2a40234587.svg': 'bbs-icon-4.svg',
            '129f63186fb6b733447f9e653575d6f73b95a280.svg': 'bbs-icon-5.svg',
            'f93c95a76591fe12c8973068e33bbb55628e387f.svg': 'bbs-title-icon.svg',
        };
        return mapping[filename] || filename;
    }

    // 下载单个图片
    async downloadImage(filename) {
        const sourceUrl = this.baseUrl + filename;
        const friendlyName = this.getFriendlyName(filename);
        const localPath = path.join(this.localDir, friendlyName);

        // 如果文件已存在，跳过
        if (fs.existsSync(localPath)) {
            console.log(`✓ 跳过 ${friendlyName} (已存在)`);
            this.imageMap.set(filename, friendlyName);
            return;
        }

        return new Promise((resolve, reject) => {
            const file = fs.createWriteStream(localPath);
            const request = http.get(sourceUrl, (response) => {
                if (response.statusCode !== 200) {
                    console.error(`❌ 下载失败 ${friendlyName}: HTTP ${response.statusCode}`);
                    fs.unlinkSync(localPath);
                    reject(new Error(`HTTP ${response.statusCode}`));
                    return;
                }

                response.pipe(file);

                file.on('finish', () => {
                    file.close();
                    console.log(`✅ 下载完成 ${friendlyName}`);
                    this.imageMap.set(filename, friendlyName);
                    resolve();
                });
            });

            request.on('error', (err) => {
                console.error(`❌ 下载失败 ${friendlyName}:`, err.message);
                fs.unlinkSync(localPath);
                reject(err);
            });

            file.on('error', (err) => {
                console.error(`❌ 文件写入失败 ${friendlyName}:`, err.message);
                fs.unlinkSync(localPath);
                reject(err);
            });
        });
    }

    // 更新文件中的图片引用
    updateFileReferences(filePath) {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  文件不存在: ${filePath}`);
            return;
        }

        let content = fs.readFileSync(filePath, 'utf8');
        let updated = false;

        // 替换所有远程图片引用
        for (const [hashName, friendlyName] of this.imageMap) {
            const oldUrl = `http://localhost:3845/assets/${hashName}`;
            const newUrl = `/assets/images/${friendlyName}`;
            
            if (content.includes(oldUrl)) {
                content = content.replace(new RegExp(oldUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newUrl);
                updated = true;
            }
        }

        if (updated) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ 更新文件引用: ${filePath}`);
        }
    }

    // 开始下载所有图片
    async downloadAll() {
        console.log('🚀 开始下载图片...\n');
        
        const images = this.getImageList();
        const promises = images.map(filename => this.downloadImage(filename));
        
        try {
            await Promise.all(promises);
            console.log('\n✅ 所有图片下载完成！');
            console.log('\n🔄 正在更新文件引用...');
            
            // 更新所有相关文件的图片引用
            const filesToUpdate = [
                'index.html',
                'index-new.html',
                'styles.css',
                'assets/css/header.css',
                'assets/css/hero.css',
                'assets/css/game-modes.css',
                'assets/css/news.css',
                'assets/css/footer.css'
            ];

            filesToUpdate.forEach(file => this.updateFileReferences(file));
            
            console.log('\n🎉 图片本地化完成！');
            console.log(`📁 图片保存在: ${this.localDir}`);
            
        } catch (error) {
            console.error('\n❌ 下载过程中出现错误:', error.message);
        }
    }
}

// 运行下载器
const downloader = new ImageDownloader();
downloader.downloadAll(); 