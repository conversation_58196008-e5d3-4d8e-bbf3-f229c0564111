/* 新闻区域 - 完全还原原版 */
.news-section {
    width: 100%;
    position: relative;
    display: flex;
    height: 580px;
    margin: 0;
}

/* 左侧容器 */
.news-left-container {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 0;
    box-sizing: border-box;
}

/* 新闻英雄区域 (左上封面) */
.news-hero {
    position: relative;
    width: 100%;
    height: 50%;
    border-bottom: 1px solid #000000;
}

.news-hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/news-cover.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.news-hero-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #000000;
    border-bottom: 1px solid #000000;
    pointer-events: none;
    border-radius: 0;
}

/* NEWS标签 */
.news-tag-container {
    position: absolute;
    left: 34px;
    top: 2px;
}

.news-tag-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 125px;
    height: 25px;
    background: #000000;
}

.news-tag-icon {
    position: absolute;
    left: 7px;
    top: 5px;
    width: 11px;
    height: 11px;
}

.news-icon-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #ffffff;
}

.news-icon-line {
    position: absolute;
    background: #ffffff;
    height: 1px;
    width: 7px;
    left: 2px;
}

.news-icon-line-1 { top: 2px; }
.news-icon-line-2 { top: 4px; }
.news-icon-line-3 { top: 6px; width: 4px; }
.news-icon-line-4 { top: 8px; }

.news-tag-text {
    position: absolute;
    left: 23px;
    top: 12.5px;
    transform: translateY(-50%);
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 12px;
    color: #ffffff;
    letter-spacing: -1px;
    line-height: 0.88;
    white-space: nowrap;
}

/* 新闻英雄区域列表 */
.news-hero-list {
    position: absolute;
    left: 40px;
    top: 50px;
    right: 40px;
    bottom: 40px;
    background: transparent;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0;
}

.news-hero-list::-webkit-scrollbar {
    width: 2px;
}

.news-hero-list::-webkit-scrollbar-track {
    background: transparent;
}

.news-hero-list::-webkit-scrollbar-thumb {
    background: rgba(1, 213, 255, 0.3);
    border-radius: 1px;
}

.news-hero-list::-webkit-scrollbar-thumb:hover {
    background: rgba(1, 213, 255, 0.5);
}

.news-hero-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    background: transparent;
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.news-hero-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: transparent;
    transition: all 0.3s ease;
}

.news-hero-item:hover::before {
    background: linear-gradient(to bottom, #01d5ff, rgba(1, 213, 255, 0.3));
}

.news-hero-item:hover {
    padding-left: 15px;
    border-bottom-color: rgba(1, 213, 255, 0.4);
}

.news-hero-item::after {
    content: '→';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.news-hero-item:hover::after {
    opacity: 1;
    color: #01d5ff;
    transform: translateY(-50%) translateX(5px);
}

.news-hero-date {
    min-width: 60px;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 12px;
    color: #000000;
    margin-right: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    background: rgba(255, 255, 255, 0.8);
    padding: 4px 8px;
    border-radius: 4px;
    text-shadow: none;
}

.news-hero-date::after {
    content: '';
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 12px;
    background: rgba(255, 255, 255, 0.2);
}

.news-hero-title {
    flex: 1;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 15px;
    color: #000000;
    line-height: 1.4;
    padding-right: 40px;
    transition: color 0.3s ease;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.news-hero-item:hover .news-hero-title {
    color: rgba(1, 213, 255, 0.9);
}

/* 新闻信息区域 */
.news-info {
    position: relative;
    width: 100%;
    height: 50%;
    margin-top: 0;
    border-top: 1px solid #000000;
}

.news-info-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(217, 217, 217, 0.9);
    backdrop-filter: blur(2.2px);
}

.news-info-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #000000;
    border-top: 1px solid #000000;
    pointer-events: none;
    border-radius: 0;
}

/* 最新信息标签 */
.news-latest-tag {
    position: absolute;
    left: 33px;
    top: -10px;
}

.news-latest-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 125px;
    height: 25px;
    background: #000000;
}

.news-latest-star {
    position: absolute;
    left: 7px;
    top: 9px;
    width: 12px;
    height: 12px;
    background-image: url('../images/news-arrow.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.news-latest-text {
    position: absolute;
    left: 23px;
    top: 12.5px;
    transform: translateY(-50%);
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 12px;
    color: #ffffff;
    letter-spacing: -1px;
    line-height: 0.88;
    white-space: nowrap;
}

/* 新闻内容 */
.news-content {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 24px;
}

.news-big-text {
    position: absolute;
    left: 15px;
    top: 50px;
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 900;
    font-size: 80px;
    color: rgba(1, 213, 255, 0.15);
    letter-spacing: -15px;
    line-height: 0.9;
    width: 150px;
    z-index: 1;
}

.news-main-content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    text-align: center;
}

.news-title {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 28px;
    color: #000000;
    letter-spacing: -1.5px;
    line-height: 1.1;
    margin-bottom: 15px;
}

.news-description {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 300;
    font-size: 16px;
    color: #000000;
    letter-spacing: -0.5px;
    line-height: 1.4;
    margin-top: 25px;
    margin-bottom: 20px;
}

/* 右侧元信息 */
.news-meta {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 15px;
}

.news-date {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 12px;
    color: #01d5ff;
    letter-spacing: 0.3px;
    line-height: 1;
    white-space: nowrap;
    background: rgba(1, 213, 255, 0.1);
    padding: 6px 12px;
    border-radius: 12px;
    border: 1px solid rgba(1, 213, 255, 0.2);
}

.news-author {
    font-family: 'MiSans VF', 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 500;
    font-size: 12px;
    color: #666666;
    letter-spacing: 0.2px;
    line-height: 1;
    white-space: nowrap;
    background: rgba(0, 0, 0, 0.05);
    padding: 6px 12px;
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.news-arrow {
    position: absolute;
    right: 30px;
    bottom: 30px;
    width: 24px;
    height: 22px;
    background-image: url('../images/news-decoration.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.news-decoration {
    position: absolute;
    right: 20px;
    top: 0;
    width: 44px;
    height: 50px;
    background-image: url('../images/news-tag-arrow.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 右侧新闻列表区域 */
.news-list-section {
    position: relative;
    width: 50%;
    height: 100%;
    background: #d9d9d9;
}

.news-list-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #d9d9d9;
}

.news-list-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #000000;
    pointer-events: none;
    border-radius: 0;
}

/* 右侧NEWS标签 */
.news-list-tag {
    position: absolute;
    left: 34px;
    top: 2px;
}

.news-list-tag-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 125px;
    height: 25px;
    background: #000000;
}

.news-list-tag-icon {
    position: absolute;
    left: 7px;
    top: 7px;
    width: 11px;
    height: 11px;
}

.news-list-icon-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #ffffff;
}

.news-list-icon-line {
    position: absolute;
    background: #ffffff;
    height: 1px;
    width: 7px;
    left: 2px;
}

.news-list-icon-line-1 { top: 2px; }
.news-list-icon-line-2 { top: 4px; }
.news-list-icon-line-3 { top: 6px; width: 4px; }
.news-list-icon-line-4 { top: 8px; }

.news-list-tag-text {
    position: absolute;
    left: 23px;
    top: 12.5px;
    transform: translateY(-50%);
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 14px;
    color: #ffffff;
    letter-spacing: -1px;
    line-height: 0.88;
    white-space: nowrap;
}

/* 右侧小封面 */
.news-list-cover {
    position: absolute;
    left: 20px;
    top: 40px;
    right: 20px;
    height: 120px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 8px;
    border: 1px solid #000000;
}

/* 新闻列表容器 */
.news-list-container {
    position: absolute;
    left: 20px;
    top: 175px;
    right: 15px;
    bottom: 80px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
    padding: 10px 5px 10px 0;
}

/* 新闻列表滚动条样式 */
.news-list-container::-webkit-scrollbar {
    width: 6px;
}

.news-list-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.news-list-container::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #01d5ff, #0099cc);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.news-list-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #00b8e6, #007aa3);
}

/* 新闻列表项目 */
.news-list-item {
    position: relative;
    display: flex;
    height: 120px;
    background: transparent;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 15px;
    gap: 15px;
}

/* 新闻图片 */
.news-item-image {
    width: 120px;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 8px;
    flex-shrink: 0;
}

/* 左侧大字 */
.news-item-left {
    width: 60px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.news-item-big-text {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 900;
    font-size: 40px;
    color: #01d5ff;
    letter-spacing: -5px;
    line-height: 0.9;
    opacity: 0.6;
}

/* 主要内容 */
.news-item-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 5px 0;
}

.news-item-title {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 20px;
    color: #000000;
    letter-spacing: -1.5px;
    line-height: 1.1;
    margin-bottom: 8px;
}

.news-item-tag {
    position: relative;
    margin-bottom: 8px;
    height: 20px;
}

.news-item-tag-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 120px;
    height: 20px;
    background: #000000;
}

.news-item-tag-icons {
    position: absolute;
    left: 7px;
    top: 7px;
}

.news-item-tag-icon {
    position: absolute;
    width: 8px;
    height: 8px;
    border: 1px solid #ffffff;
}

.news-item-tag-icon-1 {
    left: 0;
    top: 3px;
}

.news-item-tag-icon-2 {
    left: 3px;
    top: 0;
}

.news-item-tag-text {
    position: absolute;
    left: 18px;
    top: 10px;
    transform: translateY(-50%);
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 12px;
    color: #ffffff;
    letter-spacing: -1px;
    line-height: 0.88;
    white-space: nowrap;
}

.news-item-description {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #555555;
    letter-spacing: -0.5px;
    line-height: 1.4;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: 15px;
    margin-top: 15px;
    min-height: 36px;
}

/* 新闻项目底部信息 */
.news-item-bottom {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0px;
    margin-top: 0px;
}

.news-item-date {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 12px;
    color: #666666;
    letter-spacing: -0.5px;
    line-height: 1;
}

.news-item-author {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 500;
    font-size: 11px;
    color: #888888;
    letter-spacing: -0.3px;
    line-height: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .news-hero {
        height: 300px;
    }
    
    .news-info {
        height: 160px;
    }
    
    .news-big-text {
        font-size: 80px;
        letter-spacing: -15px;
    }
    
    .news-title {
        font-size: 24px;
    }
    
    .news-description {
        font-size: 16px;
        width: 500px;
    }
}

@media (max-width: 480px) {
    .news-hero {
        height: 200px;
    }
    
    .news-info {
        height: 120px;
    }
    
    .news-content {
        padding: 15px;
    }
    
    .news-big-text {
        font-size: 60px;
        letter-spacing: -10px;
        top: 40px;
    }
    
    .news-main-content {
        left: 120px;
        width: 400px;
    }
    
    .news-title {
        font-size: 18px;
        margin-bottom: 8px;
    }
    
    .news-description {
        font-size: 12px;
        width: 300px;
    }
}

/* 右侧大新闻区域 - 新设计 */
.main-news-section {
    width: 50%;
    height: 580px;
    position: relative;
    box-sizing: border-box;
}

.main-news-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #d9d9d9;
}

.main-news-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #000000;
    pointer-events: none;
}

/* 主新闻标签 */
.main-news-tag {
    position: absolute;
    left: 34px;
    top: 2px;
}

.main-news-tag-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 125px;
    height: 25px;
    background: #000000;
}

.main-news-tag-icon {
    position: absolute;
    left: 7px;
    top: 8px;
    width: 11px;
    height: 11px;
}

.main-news-icon-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid #ffffff;
}

.main-news-icon-line {
    position: absolute;
    background: #ffffff;
    height: 1px;
    width: 7px;
    left: 2px;
}

.main-news-icon-line-1 { top: 2px; }
.main-news-icon-line-2 { top: 4px; }
.main-news-icon-line-3 { top: 6px; width: 4px; }
.main-news-icon-line-4 { top: 8px; }

.main-news-tag-text {
    position: absolute;
    left: 23px;
    top: 12.5px;
    transform: translateY(-50%);
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 15px;
    color: #ffffff;
    letter-spacing: -1.35px;
    line-height: 0.88;
    white-space: nowrap;
}

/* 新闻项目 */
.main-news-item {
    position: absolute;
    width: 100%;
    height: 90px;
}

.main-news-item-1 {
    top: 80px;
}

.main-news-item-2 {
    top: 215px;
}

.main-news-item-3 {
    top: 350px;
}

/* 大字符 */
.main-news-large-char {
    position: absolute;
    left: 30px;
    top: 65px;
    transform: translateY(-50%);
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 900;
    font-size: 98px;
    color: #01d5ff;
    letter-spacing: -15px;
    line-height: 0.9;
    z-index: 0;
}

/* 左侧加号 */
.main-news-plus-left {
    position: absolute;
    left: 320px;
    top: 23px;
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 300;
    font-size: 24px;
    color: #000000;
    line-height: 0.88;
    width: 18px;
    height: 18px;
    text-align: center;
}

/* 内容区域 */
.main-news-content {
    position: absolute;
    left: 130px;
    top: 20px;
    width: 250px;
    z-index: 1;
}

.main-news-title {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 700;
    font-size: 22.5px;
    color: #000000;
    letter-spacing: -1px;
    line-height: 1.0;
    margin-bottom: 4px;
}

.main-news-description {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 300;
    font-size: 14px;
    color: #000000;
    letter-spacing: -0.5px;
    line-height: 1.1;
    margin-bottom: 5px;
    margin-top: 4px;
}

/* 新闻项目标签 */
.main-news-tag-container {
    position: relative;
    width: 130px;
    height: 20px;
    margin-bottom: 5px;
}

.main-news-item-tag-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 130px;
    height: 20px;
    background: #000000;
}

.main-news-item-tag-icon {
    position: absolute;
    left: 5px;
    top: 6px;
    width: 6px;
    height: 6px;
}

.main-news-item-icon-border-1 {
    position: absolute;
    left: 0;
    top: 2px;
    width: 6px;
    height: 6px;
    border: 1px solid #ffffff;
}

.main-news-item-icon-border-2 {
    position: absolute;
    left: 2px;
    top: 0;
    width: 6px;
    height: 6px;
    border: 1px solid #ffffff;
}

.main-news-item-tag-text {
    position: absolute;
    left: 16px;
    top: 10px;
    transform: translateY(-50%);
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 11px;
    color: #ffffff;
    letter-spacing: -0.8px;
    line-height: 0.88;
    white-space: nowrap;
}

/* 图片容器 */
.main-news-image-container {
    position: absolute;
    right: 30px;
    top: 22px;
    width: 254px;
    height: 95px;
}

.main-news-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.main-news-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(-75.47deg, rgba(217, 217, 217, 0.47) 0%, rgba(166, 166, 166, 0.01) 72.596%, rgba(115, 115, 115, 0.66) 100%);
    backdrop-filter: blur(2.2px);
}

/* 图片中的加号 */
.main-news-plus-image {
    position: absolute;
    left: 12px;
    top: 65px;
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 400;
    font-size: 16px;
    color: #000000;
    line-height: 0.88;
    width: 10px;
    height: 10px;
    text-align: center;
}

/* 元数据 */
.main-news-meta {
    position: absolute;
    left: 8px;
    top: 8px;
}

.main-news-date {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 900;
    font-size: 10px;
    color: #000000;
    letter-spacing: -0.5px;
    line-height: 0.9;
    margin-bottom: 3px;
}

.main-news-author {
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 900;
    font-size: 10px;
    color: #000000;
    letter-spacing: -0.5px;
    line-height: 0.88;
}

/* 箭头 */
.main-news-arrow {
    position: absolute;
    right: 8px;
    bottom: 8px;
    width: 16px;
    height: 16px;
}

.main-news-arrow img {
    width: 100%;
    height: 80%;
    object-fit: contain;
}

/* 装饰 */
.main-news-decoration {
    position: absolute;
    right: 0px;
    bottom: -25px;
    width: 30px;
    height: 35px;
}

.main-news-decoration img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* "点击了解更多"按钮 */
.main-news-more-button {
    position: absolute;
    right: 37px;
    top: 520px;
    width: 100px;
    height: 22px;
}

.main-news-more-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 11px;
}

.main-news-more-dot {
    position: absolute;
    left: 6px;
    top: -10%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
}

.main-news-more-dot img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.main-news-more-text {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-family: 'Noto Sans SC', 'Inter', sans-serif;
    font-weight: 600;
    font-size: 12px;
    color: #000000;
    letter-spacing: -0.8px;
    line-height: 0.88;
    width: 75px;
    height: 14px;
} 