/* 基于1080p基准的精确图片缩放系统 */

/*
基准分辨率: 1920x1080
基准尺寸记录:
- hero-logo: 1000px × 1000px
- hero-decoration: 50px × 40px
- hero-arrow: 80px × 20px
- hero-play-button: 140px × 40px
- hero-plus: font-size 35px
*/

/* 通用缩放规则 - 使用CSS变量和calc */
:root {
    --scale-factor: calc(100vw / 1920);
}



/* 应用缩放到所有元素 */
.hero-logo {
    width: calc(1000px * var(--scale-factor)) !important;
    height: calc(1000px * var(--scale-factor)) !important;
}

.hero-decoration {
    width: calc(50px * var(--scale-factor)) !important;
    height: calc(40px * var(--scale-factor)) !important;
}

.hero-arrow {
    width: calc(80px * var(--scale-factor)) !important;
    height: calc(20px * var(--scale-factor)) !important;
}

.hero-play-button {
    width: calc(140px * var(--scale-factor)) !important;
    height: calc(40px * var(--scale-factor)) !important;
}

.hero-plus {
    font-size: calc(35px * var(--scale-factor)) !important;
}

/* 导航栏元素缩放 */
.header {
    height: calc(65px * var(--scale-factor)) !important;
}

.nav-container {
    max-width: calc(1920px * var(--scale-factor)) !important;
    padding: 0 calc(20px * var(--scale-factor)) !important;
}

.logo-wrapper {
    padding-left: calc(13px * var(--scale-factor)) !important;
    padding-right: calc(45px * var(--scale-factor)) !important;
}

.logo-icon {
    width: calc(82px * var(--scale-factor)) !important;
    height: calc(65px * var(--scale-factor)) !important;
    margin-right: calc(-38px * var(--scale-factor)) !important;
    -webkit-mask-size: calc(82px * var(--scale-factor)) calc(65px * var(--scale-factor)) !important;
    mask-size: calc(82px * var(--scale-factor)) calc(65px * var(--scale-factor)) !important;
}

.logo-text {
    font-size: calc(26px * var(--scale-factor)) !important;
    line-height: calc(22px * var(--scale-factor)) !important;
    margin-right: calc(-45px * var(--scale-factor)) !important;
}

.nav-items {
    gap: calc(25px * var(--scale-factor)) !important;
    padding-right: calc(36px * var(--scale-factor)) !important;
}

.nav-item {
    height: calc(65px * var(--scale-factor)) !important;
    min-width: calc(43px * var(--scale-factor)) !important;
    padding: 0 calc(8px * var(--scale-factor)) !important;
    font-size: calc(16px * var(--scale-factor)) !important;
}

/* 调整主内容区域的上边距以匹配header高度 */
.main-content {
    margin-top: calc(65px * var(--scale-factor)) !important;
}

/* 主要板块宽度缩放 - 基于1080p的100.1%基准 */
.game-modes-section,
.news-section,
.bbs-section {
    /*
    基准: 1920px下为100.1%
    其他分辨率: 100% + (0.1% * 缩放比例)
    这样可以保持在不同分辨率下的对齐效果
    */
    width: calc(100% + 0.1% * var(--scale-factor)) !important;
}

/* Footer元素缩放 */
.site-footer {
    padding: calc(60px * var(--scale-factor)) calc(20px * var(--scale-factor)) !important;
}

.footer-links-grid {
    margin-bottom: calc(60px * var(--scale-factor)) !important;
}

.footer-links-left,
.footer-links-right {
    gap: calc(80px * var(--scale-factor)) !important;
}

.footer-column h4 {
    font-size: calc(20px * var(--scale-factor)) !important;
    margin-bottom: calc(15px * var(--scale-factor)) !important;
}

.footer-column a {
    font-size: calc(20px * var(--scale-factor)) !important;
}

.footer-column ul {
    gap: calc(8px * var(--scale-factor)) !important;
}

.footer-bottom {
    gap: calc(15px * var(--scale-factor)) !important;
}

.footer-icp,
.footer-copy {
    font-size: calc(20px * var(--scale-factor)) !important;
}

.footer-disclaimer {
    font-size: calc(16px * var(--scale-factor)) !important;
}

/* 抽奖页面元素缩放 - 基于1080p基准值 */
/* lottery-logo缩放 (基准: top: 50%, left: 30%, width: 800px, height: 700px) */
html body .lottery-page .hero-section .lottery-logo {
    top: calc(50% * 100vw / 1920) !important;
    left: calc(30% * 100vw / 1920) !important;
    width: calc(800px * 100vw / 1920) !important;
    height: calc(700px * 100vw / 1920) !important;
    position: absolute !important;
    transform: translate(-50%, -50%) !important;
}

/* lottery-image-section缩放 (基准: top: 25%, left: 70%) */
html body .lottery-page .hero-section .lottery-image-section {
    top: calc(25% * 100vw / 1920) !important;
    left: calc(70% * 100vw / 1920) !important;
    position: absolute !important;
    transform: translate(-50%, -50%) !important;
}

/* lottery-bg-image缩放 (基准: max-width: 800px, max-height: 600px) */
html body .lottery-page .hero-section .lottery-bg-image {
    max-width: calc(800px * 100vw / 1920) !important;
    max-height: calc(600px * 100vw / 1920) !important;
}

/* 限制最小和最大尺寸，避免过小或过大 */
@media (max-width: 1200px) {
    :root {
        --scale-factor: max(0.6, calc(100vw / 1920));
    }
}

@media (min-width: 3000px) {
    :root {
        --scale-factor: min(1.8, calc(100vw / 1920));
    }
}

/* 移动端特殊处理 */
@media (max-width: 768px) {
    /* Hero区域元素 */
    .hero-logo {
        width: 300px !important;
        height: 300px !important;
    }

    .hero-decoration {
        width: 30px !important;
        height: 24px !important;
    }

    .hero-arrow {
        width: 48px !important;
        height: 12px !important;
    }

    .hero-play-button {
        width: 120px !important;
        height: 36px !important;
    }

    .hero-plus {
        font-size: 28px !important;
    }

    /* 导航栏元素 - 移动端固定尺寸 */
    .header {
        height: auto !important;
        min-height: 77px !important;
    }

    .nav-container {
        padding: 10px 20px !important;
    }

    .logo-icon {
        width: 60px !important;
        height: 50px !important;
        margin-right: -25px !important;
        -webkit-mask-size: 60px 50px !important;
        mask-size: 60px 50px !important;
    }

    .logo-text {
        font-size: 18px !important;
        line-height: 16px !important;
        margin-right: -25px !important;
    }

    .nav-items {
        gap: 15px !important;
        padding-right: 0 !important;
    }

    .nav-item {
        font-size: 16px !important;
        height: 40px !important;
        min-width: 35px !important;
    }

    .main-content {
        margin-top: 120px !important;
    }

    /* 移动端板块宽度重置为100% */
    .game-modes-section,
    .news-section,
    .bbs-section {
        width: 100% !important;
    }

    /* 移动端footer样式 */
    .footer-links-grid {
        flex-direction: column !important;
        gap: 30px !important;
    }

    .footer-links-left,
    .footer-links-right {
        justify-content: flex-start !important;
        gap: 30px !important;
    }

    .footer-column h4 {
        font-size: 18px !important;
    }

    .footer-column a {
        font-size: 16px !important;
    }

    .footer-info-row {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }

    .footer-icp,
    .footer-copy {
        font-size: 16px !important;
    }

    .footer-disclaimer {
        font-size: 14px !important;
        text-align: left !important;
    }
}


